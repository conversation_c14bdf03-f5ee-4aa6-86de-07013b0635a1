import json
from datetime import datetime
from typing import List, Dict, Optional, Any
import redis
from utils.logger import Logger
from config.settings import REDIS_CONFIG

class RedisStore:
    def __init__(self, redis_url: str, exchange_id: str):
        """
        初始化Redis存储类
        
        Args:
            redis_url: Redis连接URL
            exchange_id: 交易所ID
        """
        # 使用配置参数创建连接
        self.redis_client = redis.Redis(
            host=REDIS_CONFIG['host'],
            port=REDIS_CONFIG['port'],
            db=REDIS_CONFIG['db'],
            password=REDIS_CONFIG['password'],
            decode_responses=REDIS_CONFIG['decode_responses'],
            retry_on_timeout=REDIS_CONFIG['retry_on_timeout'],
            socket_timeout=REDIS_CONFIG['socket_timeout'],
            socket_connect_timeout=REDIS_CONFIG['socket_connect_timeout']
        )
        self.exchange_id = exchange_id
        self.logger = Logger.get_logger("redis_store")
        
        # Redis键名定义
        self.contracts_hash = "contracts:contracts_data"  # 所有合约的哈希表
        self.symbols_queue = "contracts:symbols_queue"   # 合约符号队列
        self.exchange_results = "contracts:exchange_results"  # 交易所执行结果

    async def get(self, key: str, field: str = None) -> Optional[str]:
        """
        从Redis获取数据
        
        Args:
            key: Redis键名
            field: 哈希表字段名（如果key是哈希表）
            
        Returns:
            Optional[str]: 获取到的数据，如果不存在则返回None
        """
        try:
            if field is not None:
                return self.redis_client.hget(key, field)
            return self.redis_client.get(key)
        except Exception as e:
            self.logger.error(f"从Redis获取数据失败 {key}: {str(e)}")
            return None

    async def set(self, key: str, value: str, field: str = None, ttl: int = None) -> bool:
        """
        将数据存储到Redis
        
        Args:
            key: Redis键名
            value: 要存储的数据
            field: 哈希表字段名（如果key是哈希表）
            ttl: 过期时间（秒），可选
            
        Returns:
            bool: 是否成功存储
        """
        try:
            if field is not None:
                result = bool(self.redis_client.hset(key, field, value))
                if ttl is not None:
                    self.redis_client.expire(key, ttl)
                return result
            if ttl is not None:
                return bool(self.redis_client.setex(key, ttl, value))
            return bool(self.redis_client.set(key, value))
        except Exception as e:
            self.logger.error(f"存储数据到Redis失败 {key}: {str(e)}")
            return False

    async def get_all(self, key: str) -> Dict[str, str]:
        """
        获取哈希表的所有字段和值

        Args:
            key: Redis键名

        Returns:
            Dict[str, str]: 字段和值的字典
        """
        try:
            return self.redis_client.hgetall(key)
        except Exception as e:
            self.logger.error(f"获取哈希表数据失败 {key}: {str(e)}")
            return {}

    def delete(self, key: str) -> bool:
        """
        删除Redis键

        Args:
            key: Redis键名

        Returns:
            bool: 是否成功删除
        """
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            self.logger.error(f"删除Redis键失败 {key}: {str(e)}")
            return False

    def expire(self, key: str, seconds: int) -> bool:
        """
        设置键的过期时间

        Args:
            key: Redis键名
            seconds: 过期时间（秒）

        Returns:
            bool: 是否成功设置
        """
        try:
            return bool(self.redis_client.expire(key, seconds))
        except Exception as e:
            self.logger.error(f"设置过期时间失败 {key}: {str(e)}")
            return False

    def sadd(self, key: str, *values) -> int:
        """
        向SET中添加元素

        Args:
            key: Redis键名
            values: 要添加的值

        Returns:
            int: 成功添加的元素数量
        """
        try:
            return int(self.redis_client.sadd(key, *values))
        except Exception as e:
            self.logger.error(f"向SET添加元素失败 {key}: {str(e)}")
            return 0

    def smembers(self, key: str) -> set:
        """
        获取SET中的所有元素

        Args:
            key: Redis键名

        Returns:
            set: SET中的所有元素
        """
        try:
            result = self.redis_client.smembers(key)
            return {str(item) for item in result} if result else set()
        except Exception as e:
            self.logger.error(f"获取SET元素失败 {key}: {str(e)}")
            return set()

    def scard(self, key: str) -> int:
        """
        获取SET中元素的数量

        Args:
            key: Redis键名

        Returns:
            int: SET中元素的数量
        """
        try:
            return int(self.redis_client.scard(key))
        except Exception as e:
            self.logger.error(f"获取SET大小失败 {key}: {str(e)}")
            return 0

    def hset_multiple(self, key: str, mapping: Dict[str, str]) -> int:
        """
        批量设置哈希表字段

        Args:
            key: Redis键名
            mapping: 字段和值的映射

        Returns:
            int: 成功设置的字段数量
        """
        try:
            return int(self.redis_client.hset(key, mapping=mapping))
        except Exception as e:
            self.logger.error(f"批量设置哈希表字段失败 {key}: {str(e)}")
            return 0

    def hgetall(self, key: str) -> Dict[str, str]:
        """
        获取哈希表的所有字段和值

        Args:
            key: Redis键名

        Returns:
            Dict[str, str]: 字段和值的字典
        """
        try:
            result = self.redis_client.hgetall(key)
            return {str(k): str(v) for k, v in result.items()} if result else {}
        except Exception as e:
            self.logger.error(f"获取哈希表所有数据失败 {key}: {str(e)}")
            return {}

    def hlen(self, key: str) -> int:
        """
        获取哈希表中字段的数量

        Args:
            key: Redis键名

        Returns:
            int: 哈希表中字段的数量
        """
        try:
            return int(self.redis_client.hlen(key))
        except Exception as e:
            self.logger.error(f"获取哈希表大小失败 {key}: {str(e)}")
            return 0

    def lpush(self, key: str, *values) -> int:
        """
        向LIST左侧添加元素

        Args:
            key: Redis键名
            values: 要添加的值

        Returns:
            int: 添加后LIST的长度
        """
        try:
            return int(self.redis_client.lpush(key, *values))
        except Exception as e:
            self.logger.error(f"向LIST添加元素失败 {key}: {str(e)}")
            return 0

    def rpush(self, key: str, *values) -> int:
        """
        向LIST右侧添加元素

        Args:
            key: Redis键名
            values: 要添加的值

        Returns:
            int: 添加后LIST的长度
        """
        try:
            return int(self.redis_client.rpush(key, *values))
        except Exception as e:
            self.logger.error(f"向LIST添加元素失败 {key}: {str(e)}")
            return 0

    def lrange(self, key: str, start: int = 0, end: int = -1) -> List[str]:
        """
        获取LIST中指定范围的元素

        Args:
            key: Redis键名
            start: 开始索引
            end: 结束索引

        Returns:
            List[str]: 指定范围的元素列表
        """
        try:
            result = self.redis_client.lrange(key, start, end)
            return [str(item) for item in result] if result else []
        except Exception as e:
            self.logger.error(f"获取LIST元素失败 {key}: {str(e)}")
            return []

    def llen(self, key: str) -> int:
        """
        获取LIST的长度

        Args:
            key: Redis键名

        Returns:
            int: LIST的长度
        """
        try:
            return int(self.redis_client.llen(key))
        except Exception as e:
            self.logger.error(f"获取LIST长度失败 {key}: {str(e)}")
            return 0

    def _serialize_datetime(self, obj: Any) -> Any:
        """
        将datetime对象转换为ISO格式字符串
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            Any: 序列化后的对象
        """
        if isinstance(obj, datetime):
            return obj.isoformat()
        return obj

    def _serialize_for_redis(self, data: Dict) -> Dict:
        """
        递归处理字典中的所有值，将datetime对象转换为字符串
        
        Args:
            data: 要处理的数据字典
            
        Returns:
            Dict: 处理后的数据字典
        """
        return {k: self._serialize_datetime(v) for k, v in data.items()}

    def get_exchange_keys(self) -> tuple:
        """
        获取交易所相关的Redis键名
        
        Returns:
            tuple: (exchange_contracts_key, exchange_symbols_key)
        """
        exchange_contracts_key = f"contracts:{self.exchange_id}:contracts"
        exchange_symbols_key = f"contracts:{self.exchange_id}:symbols"
        return exchange_contracts_key, exchange_symbols_key

    def store_data(self, symbols: List[Dict]) -> None:
        """
        存储合约数据
        
        Args:
            symbols: 合约数据列表
        """
        try:
            self.logger.info(f"[DEBUG] {self.exchange_id} store_data symbols数量: {len(symbols)}")
            if symbols:
                self.logger.info(f"[DEBUG] {self.exchange_id} store_data 第一个样本: {symbols[0]}")
            if not symbols:
                self.logger.warning(f"交易所 {self.exchange_id} 没有数据需要存储")
                return

            # 获取交易所特定的键名
            exchange_contracts_key, exchange_symbols_key = self.get_exchange_keys()
            
            # 准备批量操作
            pipe = self.redis_client.pipeline()
            
            # 存储合约数据
            for symbol_data in symbols:
                symbol = symbol_data.get('symbol')
                if not symbol:
                    continue
                    
                # 构建合约ID
                contract_id = f"{self.exchange_id}:{symbol}"
                
                # 序列化数据，处理datetime对象
                serialized_data = self._serialize_for_redis(symbol_data)
                
                # 存储到全局哈希表
                pipe.hset(self.contracts_hash, contract_id, json.dumps(serialized_data))
                
                # 存储到交易所特定的哈希表
                pipe.hset(exchange_contracts_key, symbol, json.dumps(serialized_data))
                
                # 添加到交易所符号列表
                pipe.sadd(exchange_symbols_key, symbol)
                
                # 添加到全局符号队列
                pipe.lpush(self.symbols_queue, contract_id)
            
            # 执行批量操作
            pipe.execute()
            
            # 更新交易所执行结果
            result = {
                'exchange_id': self.exchange_id,
                'total_contracts': len(symbols),
                'timestamp': datetime.now().isoformat(),
                'status': 'success',
                'contracts': [self._serialize_for_redis(contract) for contract in symbols]
            }
            self.redis_client.hset(self.exchange_results, self.exchange_id, json.dumps(result))
            
            self.logger.success(
                f"成功存储交易所 {self.exchange_id} 的数据，"
                f"共 {len(symbols)} 个合约"
            )

        except Exception as e:
            self.logger.error(f"存储交易所 {self.exchange_id} 数据时出错: {str(e)}")
            error_result = {
                'exchange_id': self.exchange_id,
                'total_contracts': 0,
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'contracts': [],
                'error': str(e)
            }
            try:
                self.redis_client.hset(self.exchange_results, self.exchange_id, json.dumps(error_result))
            except Exception as save_error:
                self.logger.error(f"保存错误状态时出错: {str(save_error)}")

    def get_contract_data(self, symbol: str) -> Optional[Dict]:
        """
        获取特定合约的数据
        
        Args:
            symbol: 合约符号
            
        Returns:
            Optional[Dict]: 合约数据，如果不存在则返回None
        """
        try:
            symbol_key = f"{self.exchange_id}:{symbol}"
            data = self.redis_client.hget(self.contracts_hash, symbol_key)
            return json.loads(data) if data else None
        except Exception as e:
            self.logger.error(f"获取合约 {symbol} 数据时出错: {str(e)}")
            return None

    def get_exchange_contracts(self) -> List[Dict]:
        """
        获取交易所的所有合约数据
        
        Returns:
            List[Dict]: 合约数据列表
        """
        try:
            exchange_contracts_key, _ = self.get_exchange_keys()
            contracts_data = self.redis_client.hgetall(exchange_contracts_key)
            return [json.loads(data) for data in contracts_data.values()]
        except Exception as e:
            self.logger.error(f"获取交易所 {self.exchange_id} 合约数据时出错: {str(e)}")
            return []

    def get_exchange_result(self) -> Optional[Dict]:
        """
        获取交易所的执行结果
        
        Returns:
            Optional[Dict]: 执行结果数据，如果不存在则返回None
        """
        try:
            result = self.redis_client.hget(self.exchange_results, self.exchange_id)
            return json.loads(result) if result else None
        except Exception as e:
            self.logger.error(f"获取交易所 {self.exchange_id} 执行结果时出错: {str(e)}")
            return None 