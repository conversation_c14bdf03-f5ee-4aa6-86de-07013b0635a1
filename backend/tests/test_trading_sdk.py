"""
交易SDK测试文件

测试交易SDK的基本功能
"""

import asyncio
import unittest
from unittest.mock import Mock, patch, AsyncMock
from decimal import Decimal

from exchanges.trading_sdk import get_trading_sdk, ExchangeType
from exchanges.trading_base import OrderSide, OrderType, PositionSide, MarginType
from exchanges.impl.binance_trading import BinanceFuturesTrading


class TestTradingSDK(unittest.TestCase):
    """交易SDK测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.sdk = get_trading_sdk()
        self.api_key = "test_api_key"
        self.api_secret = "test_api_secret"
    
    def test_register_exchange(self):
        """测试注册交易所"""
        # 测试注册Binance期货交易所
        exchange = self.sdk.register_exchange(
            ExchangeType.BINANCE_FUTURES,
            self.api_key,
            self.api_secret
        )
        
        self.assertIsInstance(exchange, BinanceFuturesTrading)
        self.assertTrue(self.sdk.is_exchange_registered(ExchangeType.BINANCE_FUTURES))
    
    def test_get_exchange_not_registered(self):
        """测试获取未注册的交易所"""
        with self.assertRaises(ValueError):
            self.sdk.get_exchange(ExchangeType.OKX_FUTURES)
    
    def test_get_supported_exchanges(self):
        """测试获取支持的交易所列表"""
        # 注册一个交易所
        self.sdk.register_exchange(
            ExchangeType.BINANCE_FUTURES,
            self.api_key,
            self.api_secret
        )
        
        supported = self.sdk.get_supported_exchanges()
        self.assertIn("binance_futures", supported)


class TestBinanceFuturesTrading(unittest.TestCase):
    """Binance期货交易测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.api_key = "test_api_key"
        self.api_secret = "test_api_secret"
        
        # Mock BinanceFuturesAPI
        with patch('exchanges.impl.binance_trading.BinanceFuturesAPI') as mock_api:
            self.mock_api = mock_api.return_value
            self.trading = BinanceFuturesTrading(self.api_key, self.api_secret)
    
    @patch('asyncio.get_event_loop')
    def test_get_account_info(self, mock_loop):
        """测试获取账户信息"""
        # 设置mock
        mock_loop.return_value.run_in_executor.return_value = {
            "totalWalletBalance": "1000.0",
            "totalUnrealizedProfit": "50.0"
        }
        
        # 执行测试
        result = asyncio.run(self.trading.get_account_info())
        
        # 验证结果
        self.assertIn("totalWalletBalance", result)
        self.assertEqual(result["totalWalletBalance"], "1000.0")
    
    @patch('asyncio.get_event_loop')
    def test_get_order_book(self, mock_loop):
        """测试获取订单簿"""
        # 设置mock
        mock_loop.return_value.run_in_executor.return_value = {
            "bids": [["50000", "1.0"]],
            "asks": [["50001", "1.0"]]
        }
        
        # 执行测试
        result = asyncio.run(self.trading.get_order_book("BTCUSDT", 5))
        
        # 验证结果
        self.assertIn("bids", result)
        self.assertIn("asks", result)
    
    @patch('asyncio.get_event_loop')
    def test_get_ticker(self, mock_loop):
        """测试获取行情信息"""
        # 设置mock
        mock_loop.return_value.run_in_executor.return_value = {
            "bids": [["50000", "1.0"]],
            "asks": [["50001", "1.0"]]
        }
        
        # 执行测试
        result = asyncio.run(self.trading.get_ticker("BTCUSDT"))
        
        # 验证结果
        self.assertIn("symbol", result)
        self.assertEqual(result["symbol"], "BTCUSDT")
        self.assertIn("bid", result)
        self.assertIn("ask", result)


class TestTradingSDKIntegration(unittest.TestCase):
    """交易SDK集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.sdk = get_trading_sdk()
        self.api_key = "test_api_key"
        self.api_secret = "test_api_secret"
    
    @patch('exchanges.impl.binance_trading.BinanceFuturesAPI')
    @patch('asyncio.get_event_loop')
    def test_basic_trading_flow(self, mock_loop, mock_api):
        """测试基本交易流程"""
        # 设置mock
        mock_loop.return_value.run_in_executor.return_value = {
            "totalWalletBalance": "1000.0"
        }
        
        # 注册交易所
        exchange = self.sdk.register_exchange(
            ExchangeType.BINANCE_FUTURES,
            self.api_key,
            self.api_secret
        )
        
        # 测试获取账户信息
        result = asyncio.run(self.sdk.get_account_info(ExchangeType.BINANCE_FUTURES))
        self.assertIn("totalWalletBalance", result)
    
    @patch('exchanges.impl.binance_trading.BinanceFuturesAPI')
    @patch('asyncio.get_event_loop')
    def test_batch_operations(self, mock_loop, mock_api):
        """测试批量操作"""
        # 设置mock
        mock_loop.return_value.run_in_executor.return_value = []
        
        # 注册交易所
        self.sdk.register_exchange(
            ExchangeType.BINANCE_FUTURES,
            self.api_key,
            self.api_secret
        )
        
        # 测试撤销所有订单
        result = asyncio.run(self.sdk.cancel_all_orders(ExchangeType.BINANCE_FUTURES))
        self.assertIsInstance(result, list)


class TestEnums(unittest.TestCase):
    """枚举类型测试类"""
    
    def test_order_side_enum(self):
        """测试订单方向枚举"""
        self.assertEqual(OrderSide.BUY.value, "BUY")
        self.assertEqual(OrderSide.SELL.value, "SELL")
    
    def test_order_type_enum(self):
        """测试订单类型枚举"""
        self.assertEqual(OrderType.MARKET.value, "MARKET")
        self.assertEqual(OrderType.LIMIT.value, "LIMIT")
    
    def test_position_side_enum(self):
        """测试持仓方向枚举"""
        self.assertEqual(PositionSide.LONG.value, "LONG")
        self.assertEqual(PositionSide.SHORT.value, "SHORT")
    
    def test_margin_type_enum(self):
        """测试保证金类型枚举"""
        self.assertEqual(MarginType.ISOLATED.value, "ISOLATED")
        self.assertEqual(MarginType.CROSSED.value, "CROSSED")
    
    def test_exchange_type_enum(self):
        """测试交易所类型枚举"""
        self.assertEqual(ExchangeType.BINANCE_FUTURES.value, "binance_futures")


if __name__ == "__main__":
    # 运行测试
    unittest.main() 