{"timestamp": "2025-07-28T10:47:06.434534", "environment": "simulated", "tests": {"server_connectivity": true, "account_config": {"success": true, "account_level": "1", "position_mode": "long_short_mode", "permissions": "read_only,trade", "data": {"acctLv": "1", "acctStpMode": "cancel_maker", "autoLoan": false, "ctIsoMode": "automatic", "enableSpotBorrow": false, "greeksType": "PA", "ip": "", "kycLv": "2", "label": "test3t", "level": "Lv1", "levelTmp": "", "liquidationGear": "-1", "mainUid": "214616843028140032", "mgnIsoMode": "automatic", "opAuth": "0", "perm": "read_only,trade", "posMode": "long_short_mode", "roleType": "0", "spotBorrowAutoRepay": false, "spotOffsetType": "", "spotRoleType": "0", "spotTraderInsts": [], "traderInsts": [], "type": "0", "uid": "214616843028140032"}}, "market_data": {"success": true, "best_bid": "119568", "best_ask": "119568.1", "spread": 0.*****************}, "leverage_setting": {"success": true, "result": {"code": "0", "data": [{"instId": "BTC-USDT-SWAP", "lever": "3", "mgnMode": "cross", "posSide": ""}], "msg": ""}}, "position_mode_setting": {"success": false, "error": "400 Client Error: Bad Request for url: https://www.okx.com/api/v5/account/set-position-mode"}, "limit_order_buy": {"success": false, "error": "All operations failed", "order_result": {"code": "1", "data": [{"clOrdId": "", "ordId": "", "sCode": "51010", "sMsg": "You can't complete this request under your current account mode. ", "tag": "", "ts": "*************"}], "inTime": "****************", "msg": "All operations failed", "outTime": "****************"}}, "limit_order_sell": {"success": false, "error": "All operations failed", "order_result": {"code": "1", "data": [{"clOrdId": "", "ordId": "", "sCode": "51010", "sMsg": "You can't complete this request under your current account mode. ", "tag": "", "ts": "*************"}], "inTime": "****************", "msg": "All operations failed", "outTime": "****************"}}, "market_order": {"success": false, "error": "All operations failed", "order_result": {"code": "1", "data": [{"clOrdId": "", "ordId": "", "sCode": "51010", "sMsg": "You can't complete this request under your current account mode. ", "tag": "", "ts": "*************"}], "inTime": "****************", "msg": "All operations failed", "outTime": "****************"}}, "custom_order_id": {"success": true, "skipped": true, "reason": "API返回Parameter clOrdId error，暂时跳过测试"}, "position_management": {"success": true, "positions": []}, "spot_order_buy": {"success": true, "order_id": "2723560521388474368", "order_price": "1000", "order_result": {"code": "0", "data": [{"clOrdId": "", "ordId": "2723560521388474368", "sCode": "0", "sMsg": "Order placed", "tag": "", "ts": "*************"}], "inTime": "****************", "msg": "", "outTime": "*************994"}, "order_info": {"code": "0", "data": [{"accFillSz": "0", "algoClOrdId": "", "algoId": "", "attachAlgoClOrdId": "", "attachAlgoOrds": [], "avgPx": "", "cTime": "*************", "cancelSource": "", "cancelSourceReason": "", "category": "normal", "ccy": "", "clOrdId": "", "fee": "0", "feeCcy": "BTC", "fillPx": "", "fillSz": "0", "fillTime": "", "instId": "BTC-USDT", "instType": "SPOT", "isTpLimit": "false", "lever": "", "linkedAlgoOrd": {"algoId": ""}, "ordId": "2723560521388474368", "ordType": "limit", "pnl": "0", "posSide": "net", "px": "1000", "pxType": "", "pxUsd": "", "pxVol": "", "quickMgnType": "", "rebate": "0", "rebateCcy": "USDT", "reduceOnly": "false", "side": "buy", "slOrdPx": "", "slTriggerPx": "", "slTriggerPxType": "", "source": "", "state": "live", "stpId": "", "stpMode": "cancel_maker", "sz": "0.01", "tag": "", "tdMode": "cash", "tgtCcy": "", "tpOrdPx": "", "tpTriggerPx": "", "tpTriggerPxType": "", "tradeId": "", "tradeQuoteCcy": "USDT", "uTime": "*************"}], "msg": ""}}, "spot_order_sell": {"success": false, "error": "All operations failed", "order_result": {"code": "1", "data": [{"clOrdId": "", "ordId": "", "sCode": "51138", "sMsg": " ", "tag": "", "ts": "1753670829113"}], "inTime": "1753670829112592", "msg": "All operations failed", "outTime": "1753670829115399"}}}, "summary": {"total_tests": 12, "successful_tests": 6, "success_rate": "50.0%"}}