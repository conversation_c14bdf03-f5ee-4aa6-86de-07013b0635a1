#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
资金费率差异分析器配置文件

包含分析器的所有配置项和常量定义
注意：此文件将被移动到 funding_analysis/config/settings.py
"""

from typing import List, Dict

# 主交易所列表配置
MAIN_EXCHANGES: List[str] = [
    'binance',  # 币安
    'okx',      # OKX
    'bybit'     # Bybit
]

# 合约过滤配置
CONTRACT_FILTER_CONFIG = {
    # 过滤规则：正则表达式模式
    'filter_patterns': [
        r'^100\d+',  # 过滤100xx开头的合约
        r'^1000+',   # 过滤1000开头的合约
    ],
    
    # 符号标准化规则
    'normalization_rules': [
        ('USDT-PERP', 'USDT'),
        ('-PERP', ''),
        ('_USDT', '/USDT'),
        ('_USDC', '/USDC'),
        ('-USDT', '/USDT'),
        ('-USDC', '/USDC'),
    ],
    
    # 支持的报价货币
    'quote_currencies': ['USDT', 'USDC', 'USD'],
}

# Redis键名配置
ANALYZER_REDIS_KEYS = {
    # 不重复合约集合存储键
    # 存储内容：去重后的合约符号列表，包含合约数量、时间戳、涉及交易所等信息
    # 数据格式：JSON字符串，包含contracts数组、count、timestamp、exchanges字段
    'unique_contracts': 'funding_analysis:unique_contracts',

    # 资金费率差异数据存储键
    # 存储内容：按基础货币分组的费率差异详情，包含最高/最低费率交易所信息
    # 数据格式：JSON字符串，键为基础货币名称，值为差异详情对象
    'rate_differences': 'funding_analysis:rate_differences',

    # 完整分析结果存储键（API接口用）
    # 存储内容：包含成功标志、时间戳、数据和元数据的完整结果
    # 数据格式：JSON字符串，包含success、timestamp、data、metadata字段
    'analysis_results': 'funding_analysis:analysis_results',

    # 分析元数据存储键
    # 存储内容：分析过程的统计信息，包含处理时间、合约数量、交易所列表等
    # 数据格式：JSON字符串，包含analysis_time、exchanges、statistics等字段
    'analysis_metadata': 'funding_analysis:metadata',

    # 交易所合约数据存储键（预留）
    # 存储内容：按交易所分组的原始合约数据
    # 数据格式：JSON字符串，键为交易所名称，值为合约数组
    'exchange_contracts': 'funding_analysis:exchange_contracts',

    # 处理状态存储键（预留）
    # 存储内容：分析过程的实时状态信息，用于监控和进度跟踪
    # 数据格式：JSON字符串，包含status、progress、current_step等字段
    'processing_status': 'funding_analysis:status',

    # 过滤结果存储键
    # 存储内容：被过滤掉的合约列表和过滤统计信息
    # 数据格式：JSON字符串，包含filtered_contracts、filter_stats、timestamp字段
    'filtered_contracts': 'funding_analysis:filtered_contracts',

    # 详细输出数据存储键
    # 存储内容：包含过滤、不重复、差异的详细列表数据
    # 数据格式：JSON字符串，包含filtered_list、unique_list、differences_list字段
    'detailed_output': 'funding_analysis:detailed_output'
}

# 分析配置
ANALYSIS_CONFIG = {
    # 最小费率差异阈值（百分比）
    'min_rate_difference': 0.0001,  # 0.01%
    
    # 最小交易所数量要求
    'min_exchanges_count': 2,
    
    # 是否保存所有分析数据（包括无差异的）
    'save_all_data': False,
    
    # 批量处理大小
    'batch_size': 100,
    
    # 进度报告间隔
    'progress_report_interval': 100,
}

# 性能监控配置
PERFORMANCE_CONFIG = {
    # 是否启用详细性能监控
    'enable_detailed_monitoring': True,
    
    # 内存使用监控
    'monitor_memory_usage': True,
    
    # 处理时间阈值（秒）
    'slow_operation_threshold': 5.0,
    
    # 是否记录每个步骤的执行时间
    'log_step_timing': True,
}

# 日志配置
ANALYZER_LOGGING_CONFIG = {
    # 分析器专用日志级别
    'level': 'INFO',
    
    # 是否启用详细的调试信息
    'enable_debug': False,
    
    # 是否记录合约处理详情
    'log_contract_details': False,
    
    # 是否记录费率计算过程
    'log_rate_calculations': False,
}

# 数据验证配置
VALIDATION_CONFIG = {
    # 资金费率有效范围（百分比）
    'valid_rate_range': (-10.0, 10.0),
    
    # 是否验证合约符号格式
    'validate_symbol_format': True,
    
    # 是否验证时间戳
    'validate_timestamps': True,
    
    # 最大允许的数据年龄（小时）
    'max_data_age_hours': 24,
}

# 错误处理配置
ERROR_HANDLING_CONFIG = {
    # 最大重试次数
    'max_retries': 3,
    
    # 重试间隔（秒）
    'retry_interval': 1.0,
    
    # 是否在遇到错误时继续处理其他合约
    'continue_on_error': True,
    
    # 错误率阈值（超过此比例将停止处理）
    'error_rate_threshold': 0.1,  # 10%
}

# 输出格式配置
OUTPUT_CONFIG = {
    # 费率显示精度（小数位数）
    'rate_precision': 4,
    
    # 是否使用百分号显示
    'use_percentage_display': True,
    
    # 排序方式：'rate_diff', 'symbol', 'exchange'
    'default_sort_by': 'rate_diff',
    
    # 排序顺序：'asc', 'desc'
    'default_sort_order': 'desc',
    
    # 结果限制数量（0表示无限制）
    'max_results_display': 0,
}

# 缓存配置
CACHE_CONFIG = {
    # 分析结果缓存时间（秒）
    'analysis_results_ttl': 3600,  # 1小时
    
    # 合约数据缓存时间（秒）
    'contract_data_ttl': 1800,  # 30分钟
    
    # 是否启用缓存
    'enable_caching': True,
    
    # 缓存键前缀
    'cache_key_prefix': 'funding_analyzer_cache:',
}

# 导出所有配置的字典
ALL_CONFIGS = {
    'main_exchanges': MAIN_EXCHANGES,
    'contract_filter': CONTRACT_FILTER_CONFIG,
    'redis_keys': ANALYZER_REDIS_KEYS,
    'analysis': ANALYSIS_CONFIG,
    'performance': PERFORMANCE_CONFIG,
    'logging': ANALYZER_LOGGING_CONFIG,
    'validation': VALIDATION_CONFIG,
    'error_handling': ERROR_HANDLING_CONFIG,
    'output': OUTPUT_CONFIG,
    'cache': CACHE_CONFIG,
}

def get_config(config_name: str) -> Dict:
    """
    获取指定的配置
    
    Args:
        config_name: 配置名称
        
    Returns:
        Dict: 配置字典
        
    Raises:
        KeyError: 如果配置不存在
    """
    if config_name not in ALL_CONFIGS:
        raise KeyError(f"配置 '{config_name}' 不存在。可用配置: {list(ALL_CONFIGS.keys())}")
    
    return ALL_CONFIGS[config_name]

def validate_config() -> bool:
    """
    验证配置的有效性
    
    Returns:
        bool: 配置是否有效
    """
    try:
        # 验证主交易所列表不为空
        if not MAIN_EXCHANGES:
            raise ValueError("主交易所列表不能为空")
        
        # 验证费率范围
        min_rate, max_rate = VALIDATION_CONFIG['valid_rate_range']
        if min_rate >= max_rate:
            raise ValueError("费率范围配置无效")
        
        # 验证批量处理大小
        if ANALYSIS_CONFIG['batch_size'] <= 0:
            raise ValueError("批量处理大小必须大于0")
        
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False

# 在模块加载时验证配置
if __name__ == "__main__":
    if validate_config():
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败")
