#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一配置文件
包含所有系统配置项
"""

import os
import logging
from pathlib import Path
from typing import Dict
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试加载环境变量文件
env_paths = [
    Path(__file__).parent.parent / '.env',
    Path(__file__).parent / '.env',
    Path(__file__).parent / '.env.local',
]

for env_path in env_paths:
    if env_path.exists():
        logger.info(f"正在加载环境变量文件: {env_path}")
        load_dotenv(dotenv_path=env_path)
        break
else:
    logger.warning("未找到环境变量文件，将使用系统环境变量")

try:
    from .api_keys import EXCHANGE_API_KEYS
except ImportError:
    # 如果找不到 api_keys.py，使用空配置
    EXCHANGE_API_KEYS = {
        "binance": {"api_key": "", "api_secret": ""},
        "okx": {"api_key": "", "api_secret": "", "passphrase": ""},
        "bitget": {"api_key": "", "api_secret": ""}
    }

# Redis配置
def get_redis_config():
    host = os.getenv('REDIS_HOST')
    port = os.getenv('REDIS_PORT')
    db = os.getenv('REDIS_DB')
    password = os.getenv('REDIS_PASSWORD')

    # 打印当前环境变量值（不包含密码）
    logger.info(f"Redis配置 - HOST: {host}, PORT: {port}, DB: {db}")

    return {
        'host': host or 'localhost',
        'port': int(port) if port else 6379,
        'db': int(db) if db else 0,
        'password': password,
        'decode_responses': True,
        'retry_on_timeout': True,  # 超时时重试
        'socket_timeout': 5,  # 设置超时时间
        'socket_connect_timeout': 5,  # 连接超时时间
    }

REDIS_CONFIG = get_redis_config()

# Redis连接URL（用于RedisStore类）
REDIS_URL = f"redis://{REDIS_CONFIG['host']}:{REDIS_CONFIG['port']}/{REDIS_CONFIG['db']}"

# 代理配置
PROXY_CONFIG = {
    'enabled': True,  # 是否启用代理
    'host': 'geo.iproyal.com',
    'port': '12321',
    'username': 'Wu2j6U99UtUDzJaF',
    'password': 'fbTZqjVP5XF23KOL'
}

# 日志配置
LOGGING_CONFIG = {
    'log_dir': 'logs',
    'level': 'DEBUG',  # 可选: DEBUG, INFO, WARNING, ERROR, CRITICAL
    'format': '{time:YYYY-MM-DD HH:mm:ss} [{level}] [{name}:{line}] [{function}] {message}',
    'print_stack_trace': True  # 是否打印错误堆栈
}

# 合约更新配置
CONTRACT_CONFIG = {
    'update_interval': 600,  # 更新间隔（秒）
    'target_exchanges': [],  # 要获取的目标交易所列表，为空表示获取所有支持的交易所
    'debug_exchange': None,  # 调试模式下的目标交易所，设置为 None 表示调试所有交易所
}

# Redis键名配置
REDIS_KEYS = {
    'contracts_dir': 'contracts',  # 合约目录
    'exchange_results': 'contracts:exchange_results',  # 交易所执行结果
    'contracts_hash': 'contracts:contracts_hash',  # 所有合约的哈希表
    'symbols_queue': 'contracts:symbols_queue',  # 合约符号队列
}

# 交易所API配置
EXCHANGE_CONFIGS = EXCHANGE_API_KEYS

# 交易所通用配置
EXCHANGE_CONFIG = {
    'enableRateLimit': True,  # 启用请求频率限制
    'timeout': 600000,  # 超时时间（毫秒）
    'verbose': False,  # 启用详细日志
    'debug': False,    # 启用调试模式
    'keepAlive': False,  # 禁用连接复用，每次请求创建新连接
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    },
    'options': {
        'defaultType': 'future',  # 默认使用合约市场
        'adjustForTimeDifference': True,  # 自动调整时间差
        'recvWindow': 60000,  # 增加接收窗口
    }
}

# 更新间隔（秒）- 用于主程序循环
UPDATE_INTERVAL = 60

# 是否开启错误堆栈打印
OUT_EXC_INFO = True 