import sys
import traceback
from pathlib import Path
from typing import Optional
from loguru import logger
from config.settings import LOGGING_CONFIG

class Logger:
    """日志管理类
    
    用于管理应用程序的日志记录，支持控制台和文件输出，
    提供不同级别的日志记录和自定义格式化。
    """
    
    _instance: Optional['Logger'] = None
    _levels_initialized = False
    
    def __new__(cls):
        """单例模式实现，确保全局只有一个日志实例"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化日志系统"""
        if self._initialized:
            return
            
        self._initialized = True
        self._setup_logger()
    
    def _setup_logger(self):
        """配置日志系统
        
        设置日志格式、输出位置和级别：
        - 控制台输出：彩色格式化，显示时间、级别、位置和消息
        - 常规日志文件：记录所有INFO及以上级别的日志
        - 错误日志文件：仅记录ERROR及以上级别的日志
        """
        # 创建日志目录
        log_dir = Path(LOGGING_CONFIG['log_dir'])
        log_dir.mkdir(exist_ok=True)
        
        # 移除默认的处理器
        logger.remove()
        
        # 自定义日志级别（只在第一次初始化时设置）
        if not Logger._levels_initialized:
            try:
                logger.level("SUCCESS_MSG", no=25, color="<green>", icon="✅")
                logger.level("WARNING_MSG", no=30, color="<yellow>", icon="⚠️")
                logger.level("ERROR_MSG", no=40, color="<red>", icon="❌")
                logger.level("CRITICAL_MSG", no=50, color="<red><bold>", icon="💥")
                Logger._levels_initialized = True
            except ValueError:
                # 如果级别已经存在，说明是重复初始化，直接跳过
                pass
        
        # 添加控制台处理器
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level=LOGGING_CONFIG['level'],
            colorize=True,
            enqueue=True  # 启用异步日志
        )
        
        # 添加常规日志文件处理器
        logger.add(
            log_dir / "app.log",
            format="{time:YYYY-MM-DD HH:mm:ss} [{level}] [{name}:{line}] [{function}] {message}",
            level=LOGGING_CONFIG['level'],
            rotation="10 MB",
            retention="1 week",
            encoding="utf-8",
            enqueue=True  # 启用异步日志
        )
        
        # 添加错误日志文件处理器
        logger.add(
            log_dir / "error.log",
            format="{time:YYYY-MM-DD HH:mm:ss} [{level}] [{name}:{line}] [{function}] {message}",
            level="ERROR",
            rotation="10 MB",
            retention="1 month",
            encoding="utf-8",
            enqueue=True  # 启用异步日志
        )
    
    @classmethod
    def get_logger(cls, name: str):
        """获取指定名称的日志记录器
        
        Args:
            name (str): 日志记录器名称，通常使用模块名或类名
            
        Returns:
            Logger: 配置好的日志记录器实例
        """
        if cls._instance is None:
            cls()
        return logger.bind(name=name)

    @staticmethod
    def _format_exception(e: Exception) -> str:
        """格式化异常信息"""
        if LOGGING_CONFIG.get('print_stack_trace', True):
            return f"{str(e)}\n{traceback.format_exc()}"
        return str(e)
    
    @staticmethod
    def error(msg: str, e: Exception = None, **kwargs):
        """记录错误日志"""
        if e:
            msg = f"{msg}: {Logger._format_exception(e)}"
        logger.error(msg, **kwargs)
    
    @staticmethod
    def warning(msg: str, e: Exception = None, **kwargs):
        """记录警告日志"""
        if e:
            msg = f"{msg}: {Logger._format_exception(e)}"
        logger.warning(msg, **kwargs)
    
    @staticmethod
    def info(msg: str, **kwargs):
        """记录信息日志"""
        logger.info(msg, **kwargs)
    
    @staticmethod
    def debug(msg: str, **kwargs):
        """记录调试日志"""
        logger.debug(msg, **kwargs)
    
    @staticmethod
    def success(msg: str, **kwargs):
        """记录成功日志"""
        logger.success(msg, **kwargs)

# 创建默认日志记录器
default_logger = Logger.get_logger("app")

def log_success(func):
    """成功日志装饰器
    
    用于标记成功执行的函数，自动记录成功日志。
    日志格式：{函数名} 执行成功
    
    Args:
        func: 被装饰的异步函数
        
    Returns:
        wrapper: 包装后的函数
    """
    async def wrapper(*args, **kwargs):
        result = await func(*args, **kwargs)
        logger.log("SUCCESS_MSG", f"{func.__name__} 执行成功")
        return result
    return wrapper

def log_warning(func):
    """警告日志装饰器
    
    用于标记可能产生警告的函数，在发生异常时记录警告日志。
    日志格式：{函数名} 执行警告: {错误信息}
    
    Args:
        func: 被装饰的异步函数
        
    Returns:
        wrapper: 包装后的函数
    """
    async def wrapper(*args, **kwargs):
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            logger.log("WARNING_MSG", f"{func.__name__} 执行警告: {str(e)}")
            raise
    return wrapper

def log_error(func):
    """错误日志装饰器
    
    用于标记可能产生错误的函数，在发生异常时记录错误日志。
    日志格式：{函数名} 执行失败: {错误信息}
    
    Args:
        func: 被装饰的异步函数
        
    Returns:
        wrapper: 包装后的函数
    """
    async def wrapper(*args, **kwargs):
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            logger.log("ERROR_MSG", f"{func.__name__} 执行失败: {str(e)}")
            raise
    return wrapper 