import time
from datetime import datetime

class PerformanceMonitor:
    """
    性能监控类
    
    用于监控代码执行时间和性能指标。
    """
    
    def __init__(self):
        """初始化性能监控器"""
        self.start_time = None
        self.end_time = None
    
    def start(self) -> None:
        """开始计时"""
        self.start_time = time.time()
    
    def end(self) -> float:
        """
        结束计时并返回执行时间
        
        Returns:
            float: 执行时间（秒）
        """
        self.end_time = time.time()
        return self.end_time - self.start_time
    
    def get_execution_time(self) -> float:
        """
        获取执行时间
        
        Returns:
            float: 执行时间（秒）
        """
        if not self.start_time:
            return 0.0
        if not self.end_time:
            return time.time() - self.start_time
        return self.end_time - self.start_time
    
    def get_formatted_time(self) -> str:
        """
        获取格式化的执行时间
        
        Returns:
            str: 格式化的执行时间，例如 "1.234秒"
        """
        execution_time = self.get_execution_time()
        return f"{execution_time:.3f}秒"
    
    def reset(self) -> None:
        """重置计时器"""
        self.start_time = None
        self.end_time = None 