import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
from exchanges.trading_base import (
    TradingBase, OrderSide, OrderType, PositionSide, 
    MarginType, OrderStatus, TradingError, InsufficientBalanceError
)
from common_src.binance.binance_exchange import BinanceFuturesAPI


class BinanceFuturesTrading(TradingBase):
    """
    Binance期货交易实现类
    
    基于BinanceFuturesAPI封装，提供统一的交易接口
    """
    
    def __init__(self, api_key: str, api_secret: str):
        super().__init__("binance_futures", api_key, api_secret)
        self._api = BinanceFuturesAPI(api_key, api_secret)
        self.logger.info("Binance期货交易接口初始化完成")

    async def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.check_api_permissions)
            return result
        except Exception as e:
            self.logger.error(f"获取账户信息失败: {e}")
            raise TradingError(f"获取账户信息失败: {e}")

    async def get_order_book(self, symbol: str, limit: int = 20) -> Dict[str, Any]:
        """获取订单簿深度"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.get_order_book, symbol, limit)
            return result
        except Exception as e:
            self.logger.error(f"获取订单簿失败: {e}")
            raise TradingError(f"获取订单簿失败: {e}")

    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """获取交易对行情信息"""
        try:
            # 使用订单簿的前几档来获取价格信息
            order_book = await self.get_order_book(symbol, 1)
            return {
                "symbol": symbol,
                "bid": float(order_book["bids"][0][0]) if order_book["bids"] else 0,
                "ask": float(order_book["asks"][0][0]) if order_book["asks"] else 0,
                "bidSize": float(order_book["bids"][0][1]) if order_book["bids"] else 0,
                "askSize": float(order_book["asks"][0][1]) if order_book["asks"] else 0,
            }
        except Exception as e:
            self.logger.error(f"获取行情信息失败: {e}")
            raise TradingError(f"获取行情信息失败: {e}")

    async def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        quantity: Union[float, Decimal],
        price: Optional[Union[float, Decimal]] = None,
        position_side: Optional[PositionSide] = None,
        time_in_force: str = "GTC",
        **kwargs
    ) -> Dict[str, Any]:
        """下单"""
        try:
            loop = asyncio.get_event_loop()
            
            # 根据订单类型和方向调用相应的API
            if order_type == OrderType.MARKET:
                if side == OrderSide.BUY:
                    result = await loop.run_in_executor(
                        None, 
                        self._api.place_market_buy,
                        symbol, float(quantity), kwargs.get('margin_type', 'CROSSED'),
                        kwargs.get('leverage', 2), kwargs.get('offset_percent', 0)
                    )
                else:
                    result = await loop.run_in_executor(
                        None,
                        self._api.place_market_sell,
                        symbol, float(quantity), kwargs.get('margin_type', 'CROSSED'),
                        kwargs.get('leverage', 2), kwargs.get('offset_percent', 0)
                    )
            elif order_type == OrderType.LIMIT:
                if price is None:
                    raise TradingError("限价单必须指定价格")
                
                # 使用内部方法进行下单
                response = await loop.run_in_executor(
                    None,
                    self._api._place_order,
                    symbol, side.value, 0, float(quantity), float(price),
                    kwargs.get('margin_type', 'CROSSED'), kwargs.get('leverage', 2),
                    'coin', kwargs.get('offset_percent', 0)
                )
                
                if response.status_code == 200:
                    result = response.json()
                else:
                    error_data = response.json()
                    raise TradingError(f"下单失败: {error_data.get('msg', '未知错误')}")
            else:
                raise TradingError(f"不支持的订单类型: {order_type}")
            
            return result
        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            raise TradingError(f"下单失败: {e}")

    async def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """撤销订单"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.cancel_order, symbol, int(order_id))
            return result
        except Exception as e:
            self.logger.error(f"撤销订单失败: {e}")
            raise TradingError(f"撤销订单失败: {e}")

    async def get_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """查询订单详情"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.get_order_info, symbol, int(order_id))
            return result
        except Exception as e:
            self.logger.error(f"查询订单失败: {e}")
            raise TradingError(f"查询订单失败: {e}")

    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """查询未成交订单"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.query_open_orders, symbol)
            return result
        except Exception as e:
            self.logger.error(f"查询未成交订单失败: {e}")
            raise TradingError(f"查询未成交订单失败: {e}")

    async def get_positions(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """查询持仓信息"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.query_position_info, symbol)
            return result
        except Exception as e:
            self.logger.error(f"查询持仓失败: {e}")
            raise TradingError(f"查询持仓失败: {e}")

    async def set_leverage(self, symbol: str, leverage: int) -> Dict[str, Any]:
        """设置杠杆倍数"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.set_leverage, symbol, leverage)
            return result
        except Exception as e:
            self.logger.error(f"设置杠杆倍数失败: {e}")
            raise TradingError(f"设置杠杆倍数失败: {e}")

    async def set_margin_type(self, symbol: str, margin_type: MarginType) -> Dict[str, Any]:
        """设置保证金类型"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._api.set_margin_type, symbol, margin_type.value)
            return result
        except Exception as e:
            self.logger.error(f"设置保证金类型失败: {e}")
            raise TradingError(f"设置保证金类型失败: {e}")

    # 重写便捷方法以适配Binance API
    async def place_market_buy(
        self,
        symbol: str,
        quantity: Union[float, Decimal],
        position_side: Optional[PositionSide] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """市价买入"""
        return await self._api.place_buy_order(
            symbol=symbol,
            usdc_amount=float(quantity),
            margin_type=kwargs.get('margin_type', 'CROSSED'),
            leverage=kwargs.get('leverage', 2),
            offset_percent=kwargs.get('offset_percent', 0)
        )

    async def place_market_sell(
        self,
        symbol: str,
        quantity: Union[float, Decimal],
        position_side: Optional[PositionSide] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """市价卖出"""
        # 这里需要先获取订单ID，然后调用平仓方法
        # 由于Binance API的特殊性，这里简化处理
        return await self.place_order(
            symbol=symbol,
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=quantity,
            position_side=position_side,
            **kwargs
        )

    async def clear_all_positions(self) -> List[Dict[str, Any]]:
        """清理所有持仓和订单"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._api.clear_order)
            return [{"status": "success", "message": "清理完成"}]
        except Exception as e:
            self.logger.error(f"清理持仓失败: {e}")
            raise TradingError(f"清理持仓失败: {e}")

    async def get_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """获取资金费率（继承自父类）"""
        # 这里可以实现获取资金费率的逻辑
        # 暂时返回空实现
        return {
            "symbol": symbol,
            "funding_rate": 0.0,
            "mark_price": 0.0,
            "next_funding_time": None
        }

    async def get_all_funding_rates(self) -> List[Dict[str, Any]]:
        """获取所有资金费率（继承自父类）"""
        # 这里可以实现获取所有资金费率的逻辑
        # 暂时返回空实现
        return [] 