import asyncio
import json
from typing import Dict, List, Optional
from datetime import datetime
import time
import argparse
from exchanges import ExchangeFactory
from config.settings import (
    EXCHANGE_CONFIGS,
    REDIS_CONFIG,
    UPDATE_INTERVAL,
    OUT_EXC_INFO, REDIS_URL,
    CONTRACT_CONFIG
)
from exchanges.base import ExchangeBase
from store.redis_store import RedisStore
from utils.logger import Logger, log_success, log_warning, log_error
from utils.monitor import PerformanceMonitor

# 获取日志记录器
logger = Logger.get_logger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='交易所资金费率监控程序')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--exchange', type=str, help='指定要处理的交易所（例如：binance, okx, bitget）')
    parser.add_argument('--interval', type=int, help='更新间隔（秒）')
    return parser.parse_args()

@log_error
async def fetch_all_exchange_rates() -> Dict[str, List[Dict]]:
    """
    获取所有交易所的费率信息
    
    Returns:
        Dict[str, List[Dict]]: 按交易所名称组织的费率信息
    """
    start_time = time.time()
    results: Dict[str, List[Dict]] = {}
    
    # 获取要处理的交易所列表
    if CONTRACT_CONFIG.get('debug_exchange'):
        exchanges = [CONTRACT_CONFIG['debug_exchange']]
        logger.info(f"将只处理 {CONTRACT_CONFIG['debug_exchange']} 交易所")
    else:
        exchanges = ExchangeFactory.get_available_exchanges()
        logger.info(f"将处理所有支持的交易所: {', '.join(exchanges)}")
    
    # 创建任务列表
    tasks = []
    for exchange_name in exchanges:
        logger.info(f"开始获取 {exchange_name} 的费率信息")
        try:
            # 使用配置文件中的API配置
            config = EXCHANGE_CONFIGS.get(exchange_name, {})
            if not config:
                logger.warning(f"未找到 {exchange_name} 的配置信息")
                
            # 创建交易所实例
            exchange = ExchangeFactory.create_exchange(
                exchange_name,
                api_key=config.get("api_key", ""),
                api_secret=config.get("api_secret", "")
            )
            
            # 创建异步任务
            task = asyncio.create_task(fetch_exchange_rates(exchange, exchange_name))
            tasks.append(task)
            
        except Exception as e:
            logger.error(f"创建 {exchange_name} 任务失败: {str(e)}", exc_info=True)
            continue
    
    # 等待所有任务完成
    if tasks:
        completed_results = await asyncio.gather(*tasks, return_exceptions=True)
        for exchange_name, result in zip(exchanges, completed_results):
            if isinstance(result, Exception):
                logger.error(f"获取 {exchange_name} 费率信息失败", result)
                results[exchange_name] = []
            else:
                results[exchange_name] = result
                if not result:  # 如果返回空列表，也记录一下
                    logger.warning(f"{exchange_name} 未返回任何合约数据")
    
    # 添加详细的统计信息
    total_contracts = sum(len(contracts) for contracts in results.values())
    successful_exchanges = sum(1 for contracts in results.values() if contracts)
    failed_exchanges = len(exchanges) - successful_exchanges
    
    logger.info("=" * 70)
    logger.info("📊 执行结果统计:")
    logger.info(f"✅ 成功获取数据的交易所: {successful_exchanges} 个")
    logger.info(f"❌ 获取数据失败的交易所: {failed_exchanges} 个")
    logger.info(f"📈 总共获取到合约数量: {total_contracts} 个")
    
    # 打印每个交易所的详细信息
    logger.info("\n📝 各交易所详情:")
    for exchange_name, contracts in results.items():
        status = "✅" if contracts else "❌"
        logger.info(f"{status} {exchange_name}: {len(contracts)} 个合约")
    
    logger.info("=" * 70)
    logger.info("✅ 程序执行完成")
    logger.info(f"⏱️ 总执行时间: {time.time() - start_time:.2f} 秒")
    logger.info("=" * 70)
    
    return results

async def fetch_exchange_rates(exchange: ExchangeBase, exchange_name: str) -> List[Dict]:
    """
    获取单个交易所的费率信息
    
    Args:
        exchange: 交易所实例
        exchange_name: 交易所名称
        
    Returns:
        List[Dict]: 费率信息列表
    """
    try:
        # 添加性能监控
        monitor = PerformanceMonitor()
        monitor.start()
        
        rates = await exchange.get_all_funding_rates()
        execution_time = monitor.end()
        
        logger.success(
            f"成功获取 {exchange_name} 的费率信息，"
            f"共 {len(rates)} 个交易对，"
            f"耗时 {execution_time:.2f} 秒"
        )
        
        return rates
    except Exception as e:
        logger.error(f"获取 {exchange_name} 费率信息失败: {str(e)}", exc_info=True)
        return []

@log_success
async def main():
    """
    主程序入口
    
    主要功能：
    1. 定期从各个交易所获取资金费率信息
    2. 将数据存储到Redis
    3. 打印实时费率信息
    4. 异常处理和日志记录
    """
    # 解析命令行参数
    args = parse_args()
    
    # 更新配置
    if args.exchange:
        CONTRACT_CONFIG['debug_exchange'] = args.exchange.lower()
        logger.info(f"将只处理 {args.exchange} 交易所")
    
    if args.debug:
        logger.info("调试模式已启用")
    
    if args.interval:
        global UPDATE_INTERVAL
        UPDATE_INTERVAL = args.interval
        logger.info(f"更新间隔已设置为 {args.interval} 秒")
    
    logger.info("程序启动")
    try:
        while True:
            cycle_start_time = time.time()
            
            # 获取所有交易所的费率信息
            rates = await fetch_all_exchange_rates()
            
            # 存储数据到Redis
            for exchange_name, exchange_rates in rates.items():
                try:
                    redis_store = RedisStore(REDIS_URL, exchange_name)
                    redis_store.store_data(exchange_rates)
                    logger.success(f"成功将 {exchange_name} 的数据存储到Redis")
                except Exception as e:
                    logger.error(f"存储 {exchange_name} 数据到Redis失败: {str(e)}", exc_info=True)
            
            # 打印结果
            for exchange_name, exchange_rates in rates.items():
                logger.info(f"{exchange_name} 费率信息:")
                for rate in exchange_rates:
                    logger.debug(f"funding_rate: {json.dumps(rate, indent=2)}")
                    logger.info(
                        f"交易对: {rate.get('symbol', 'N/A')}, "
                        f"资金费率: {rate.get('fundingRate', -9999)}, "
                        f"标记价格: {rate.get('mark_price', -9999)}, "
                        f"下次结算时间: {rate.get('nextFundingTime', 'N/A')}"
                    )
            
            # 计算本次循环耗时
            cycle_time = time.time() - cycle_start_time
            wait_time = max(0, UPDATE_INTERVAL - cycle_time)
            
            # 等待下一次更新
            logger.info(
                f"本次循环耗时: {cycle_time:.2f} 秒, "
                f"等待 {wait_time:.2f} 秒后进行下一次更新..."
            )
            await asyncio.sleep(wait_time)
    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}", exc_info=True)
        raise
    finally:
        logger.info("程序退出")

if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main()) 