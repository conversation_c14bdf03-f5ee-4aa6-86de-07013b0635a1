#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
资金费率差异分析结果查询脚本

用于查询和显示保存在Redis中的分析结果
"""

import asyncio
import json
import sys
from typing import Optional, Dict
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config.settings import REDIS_URL
from store.redis_store import RedisStore
from utils.logger import Logger
from funding_analysis.config.settings import AnalyzerConfig
from funding_analysis.utils.formatter import DataFormatter

logger = Logger.get_logger("query_results")

class ResultsQuery:
    """结果查询类"""
    
    def __init__(self):
        """初始化查询器"""
        self.redis_store = RedisStore(REDIS_URL, "analyzer")
        self.config = AnalyzerConfig()
        self.formatter = DataFormatter()
    
    async def get_analysis_metadata(self) -> Optional[Dict]:
        """获取分析元数据"""
        try:
            data = await self.redis_store.get(self.config.REDIS_KEYS['analysis_metadata'])
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取分析元数据失败: {e}")
            return None
    
    async def get_rate_differences(self) -> Optional[Dict]:
        """获取费率差异数据"""
        try:
            data = await self.redis_store.get(self.config.REDIS_KEYS['rate_differences'])
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取费率差异数据失败: {e}")
            return None
    
    async def get_unique_contracts(self) -> Optional[Dict]:
        """获取不重复合约数据"""
        try:
            data = await self.redis_store.get(self.config.REDIS_KEYS['unique_contracts'])
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取不重复合约数据失败: {e}")
            return None
    
    async def get_analysis_results(self) -> Optional[Dict]:
        """获取完整分析结果"""
        try:
            data = await self.redis_store.get(self.config.REDIS_KEYS['analysis_results'])
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取完整分析结果失败: {e}")
            return None
    
    async def get_filtered_contracts(self) -> Optional[Dict]:
        """获取过滤的合约数据"""
        try:
            data = await self.redis_store.get(self.config.REDIS_KEYS['filtered_contracts'])
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取过滤合约数据失败: {e}")
            return None
    
    async def get_detailed_output(self) -> Optional[Dict]:
        """获取详细输出数据"""
        try:
            data = await self.redis_store.get(self.config.REDIS_KEYS['detailed_output'])
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取详细输出数据失败: {e}")
            return None
    
    def print_metadata(self, metadata: Dict):
        """打印元数据"""
        logger.info("=" * 80)
        logger.info("📊 分析元数据")
        logger.info("=" * 80)
        
        analysis_time = metadata.get('analysis_time', 'N/A')
        exchanges = metadata.get('exchanges', [])
        total_differences = metadata.get('total_differences', 0)
        
        logger.info(f"🕐 分析时间: {analysis_time}")
        logger.info(f"🏢 交易所: {', '.join(exchanges)}")
        logger.info(f"💰 发现差异数: {total_differences}")
        
        statistics = metadata.get('statistics', {})
        logger.info(f"📈 总加载合约数: {statistics.get('total_contracts_loaded', 0)}")
        logger.info(f"🔍 过滤合约数: {statistics.get('filtered_contracts', 0)}")
        logger.info(f"🎯 不重复合约数: {statistics.get('unique_contracts', 0)}")
        logger.info(f"⚡ 分析合约数: {statistics.get('analyzed_contracts', 0)}")
        logger.info(f"⏱️ 处理时间: {statistics.get('processing_time', 0):.2f} 秒")
    
    def print_top_differences(self, rate_differences: Dict, limit: int = 20):
        """打印前N个最大差异"""
        logger.info("=" * 80)
        logger.info(f"🔥 前{limit}个最大费率差异")
        logger.info("=" * 80)
        
        # 按费率差异排序
        sorted_diffs = sorted(
            rate_differences.values(),
            key=lambda x: x.get('rate_diff', 0),
            reverse=True
        )[:limit]
        
        for i, diff in enumerate(sorted_diffs, 1):
            base = diff.get('base', 'N/A')
            symbol = diff.get('symbol', 'N/A')
            rate_diff_str = diff.get('rate_diff_str', 'N/A')
            min_exchange = diff.get('min_exchange', 'N/A')
            min_rate_str = diff.get('min_rate_str', 'N/A')
            max_exchange = diff.get('max_exchange', 'N/A')
            max_rate_str = diff.get('max_rate_str', 'N/A')
            exchanges_count = diff.get('exchanges_count', 0)
            
            logger.info(
                f"{i:2d}. {base:10s} ({symbol:15s}) | "
                f"差异: {rate_diff_str:8s} | "
                f"最低: {min_exchange:8s} ({min_rate_str:8s}) | "
                f"最高: {max_exchange:8s} ({max_rate_str:8s}) | "
                f"交易所数: {exchanges_count}"
            )
    
    def print_exchange_statistics(self, rate_differences: Dict):
        """打印交易所统计"""
        logger.info("=" * 80)
        logger.info("📈 交易所统计信息")
        logger.info("=" * 80)
        
        exchange_stats = {}
        
        for diff in rate_differences.values():
            min_exchange = diff.get('min_exchange')
            max_exchange = diff.get('max_exchange')
            
            if min_exchange:
                if min_exchange not in exchange_stats:
                    exchange_stats[min_exchange] = {'min_count': 0, 'max_count': 0}
                exchange_stats[min_exchange]['min_count'] += 1
            
            if max_exchange:
                if max_exchange not in exchange_stats:
                    exchange_stats[max_exchange] = {'min_count': 0, 'max_count': 0}
                exchange_stats[max_exchange]['max_count'] += 1
        
        for exchange, stats in exchange_stats.items():
            min_count = stats['min_count']
            max_count = stats['max_count']
            total = min_count + max_count
            
            logger.info(
                f"{exchange:10s} | "
                f"最低费率次数: {min_count:3d} | "
                f"最高费率次数: {max_count:3d} | "
                f"总计: {total:3d}"
            )
    
    def print_detailed_output(self, detailed_data: Dict):
        """打印详细输出数据"""
        logger.info("=" * 80)
        logger.info("📋 详细输出数据")
        logger.info("=" * 80)
        
        summary = detailed_data.get('summary', {})
        logger.info(f"📊 数据摘要:")
        logger.info(f"   过滤合约数: {summary.get('total_filtered', 0)}")
        logger.info(f"   不重复合约数: {summary.get('total_unique', 0)}")
        logger.info(f"   分析合约数: {summary.get('total_analyzed', 0)}")
        logger.info(f"   有差异合约数: {summary.get('total_with_differences', 0)}")
        
        # 显示过滤的合约
        filtered_contracts = detailed_data.get('filtered_contracts', [])
        if filtered_contracts:
            logger.info(f"\n🔍 被过滤的合约 (前10个):")
            for i, contract in enumerate(filtered_contracts[:10], 1):
                logger.info(
                    f"  {i:2d}. {contract['symbol']:20s} | "
                    f"交易所: {contract['exchange']:8s} | "
                    f"费率: {contract['funding_rate']:>8s}"
                )
            if len(filtered_contracts) > 10:
                logger.info(f"     ... 还有 {len(filtered_contracts) - 10} 个")
        
        # 显示不重复合约
        unique_contracts = detailed_data.get('unique_contracts', [])
        if unique_contracts:
            # 按交易所数量分组
            by_count = {}
            for contract in unique_contracts:
                count = contract['exchange_count']
                if count not in by_count:
                    by_count[count] = []
                by_count[count].append(contract)
            
            logger.info(f"\n🎯 不重复合约分布:")
            for count in sorted(by_count.keys(), reverse=True):
                contracts = by_count[count]
                logger.info(f"   存在于 {count} 个交易所: {len(contracts)} 个合约")
                
                # 显示前5个例子
                for i, contract in enumerate(contracts[:5], 1):
                    exchanges_str = ', '.join(contract['exchanges'])
                    logger.info(f"     {i}. {contract['symbol']:15s} ({exchanges_str})")
                if len(contracts) > 5:
                    logger.info(f"     ... 还有 {len(contracts) - 5} 个")
        
        # 显示差异分析
        differences = detailed_data.get('differences_analysis', [])
        if differences:
            with_diff = [d for d in differences if d['has_difference']]
            without_diff = [d for d in differences if not d['has_difference']]
            
            logger.info(f"\n💰 差异分析详情:")
            logger.info(f"   有差异: {len(with_diff)} 个")
            logger.info(f"   无差异: {len(without_diff)} 个")
            
            if with_diff:
                # 按差异排序
                sorted_diff = sorted(with_diff, key=lambda x: x.get('rate_diff', 0), reverse=True)
                logger.info(f"\n   前10个最大差异:")
                for i, diff in enumerate(sorted_diff[:10], 1):
                    symbol = diff['symbol']
                    rate_diff = diff.get('rate_diff', 0)
                    min_ex = diff.get('min_exchange', 'N/A')
                    max_ex = diff.get('max_exchange', 'N/A')
                    logger.info(
                        f"     {i:2d}. {symbol:15s} | "
                        f"差异: {rate_diff:.4f}% | "
                        f"{min_ex} -> {max_ex}"
                    )
    
    async def query_and_display(self):
        """查询并显示所有结果"""
        logger.info("🔍 开始查询分析结果...")
        
        # 获取分析元数据
        metadata = await self.get_analysis_metadata()
        if metadata:
            self.print_metadata(metadata)
        else:
            logger.warning("未找到分析元数据")
        
        # 获取费率差异数据
        rate_differences = await self.get_rate_differences()
        if rate_differences:
            self.print_top_differences(rate_differences)
            self.print_exchange_statistics(rate_differences)
        else:
            logger.warning("未找到费率差异数据")
        
        # 获取不重复合约数据
        unique_contracts = await self.get_unique_contracts()
        if unique_contracts:
            logger.info("=" * 80)
            logger.info("📋 合约信息摘要")
            logger.info("=" * 80)
            logger.info(f"🎯 不重复合约总数: {unique_contracts.get('count', 0)}")
            logger.info(f"🕐 生成时间: {unique_contracts.get('timestamp', 'N/A')}")
            logger.info(f"🏢 涉及交易所: {', '.join(unique_contracts.get('exchanges', []))}")
        else:
            logger.warning("未找到不重复合约数据")
        
        # 获取详细输出数据
        detailed_output = await self.get_detailed_output()
        if detailed_output:
            self.print_detailed_output(detailed_output)
        else:
            logger.warning("未找到详细输出数据")

async def main():
    """主程序入口"""
    logger.info("=" * 80)
    logger.info("🔍 资金费率差异分析结果查询")
    logger.info("=" * 80)
    
    try:
        query = ResultsQuery()
        await query.query_and_display()
        
    except Exception as e:
        logger.error(f"查询过程中发生错误: {str(e)}")
        raise
    finally:
        logger.info("=" * 80)
        logger.info("✅ 查询完成")
        logger.info("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
