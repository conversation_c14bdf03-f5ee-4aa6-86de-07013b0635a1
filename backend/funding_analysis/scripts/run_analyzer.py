#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
资金费率差异分析器运行脚本

提供命令行接口来运行分析器
"""

import asyncio
import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import Logger
from funding_analysis.core.analyzer import FundingRateAnalyzer
from funding_analysis.config.settings import AnalyzerConfig

logger = Logger.get_logger("run_analyzer")

async def dummy_save(*args, **kwargs):
    """干运行模式的虚拟保存函数"""
    logger.info("干运行模式：跳过保存操作")

async def run_analysis_with_options(args):
    """
    根据命令行参数运行分析
    
    Args:
        args: 命令行参数
    """
    try:
        # 创建分析器实例
        analyzer = FundingRateAnalyzer()
        
        # 如果是干运行模式，替换保存方法
        if args.dry_run:
            logger.info("🔍 干运行模式：将执行分析但不保存结果")
            analyzer.save_unique_contracts = dummy_save
            analyzer.save_analysis_results = dummy_save
            # 注意：save_detailed_output 仍然执行，因为它用于显示详细信息
        
        # 如果指定了自定义交易所
        if args.exchanges:
            analyzer.config.MAIN_EXCHANGES = args.exchanges
            logger.info(f"使用自定义交易所: {', '.join(args.exchanges)}")
        
        # 如果指定了自定义费率差异阈值
        if args.min_rate_diff:
            analyzer.config.ANALYSIS_CONFIG['min_rate_difference'] = args.min_rate_diff
            logger.info(f"使用自定义费率差异阈值: {args.min_rate_diff}")
        
        # 运行分析
        await analyzer.run_analysis()
        
        if args.dry_run:
            logger.info("🔍 干运行完成，未保存任何数据")
        
    except Exception as e:
        logger.error(f"运行分析失败: {str(e)}")
        raise

def check_config():
    """检查配置"""
    logger.info("🔧 检查配置...")
    
    config = AnalyzerConfig()
    if config.validate_config():
        logger.success("✅ 配置验证通过")
        
        # 显示主要配置
        logger.info("📋 主要配置:")
        logger.info(f"  交易所: {', '.join(config.MAIN_EXCHANGES)}")
        logger.info(f"  最小费率差异: {config.ANALYSIS_CONFIG['min_rate_difference']}")
        logger.info(f"  最小交易所数量: {config.ANALYSIS_CONFIG['min_exchanges_count']}")
        logger.info(f"  批量处理大小: {config.ANALYSIS_CONFIG['batch_size']}")
        
        return True
    else:
        logger.error("❌ 配置验证失败")
        return False

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(
        description="资金费率差异分析器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_analyzer.py                    # 运行完整分析
  python run_analyzer.py --dry-run          # 干运行模式
  python run_analyzer.py --verbose          # 详细输出模式
  python run_analyzer.py --exchanges binance okx  # 指定交易所
  python run_analyzer.py --min-rate-diff 0.001    # 自定义阈值
  python run_analyzer.py --config-check     # 检查配置
        """
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式：执行分析但不保存结果'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--exchanges',
        nargs='+',
        choices=['binance', 'okx', 'bybit'],
        help='指定要分析的交易所'
    )
    
    parser.add_argument(
        '--min-rate-diff',
        type=float,
        help='最小费率差异阈值（百分比）'
    )
    
    parser.add_argument(
        '--config-check',
        action='store_true',
        help='检查配置并退出'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        Logger.set_level("DEBUG")
    
    logger.info("=" * 80)
    logger.info("🎯 资金费率差异分析器")
    logger.info("=" * 80)
    logger.info(f"📋 目标交易所: {', '.join(args.exchanges or AnalyzerConfig.MAIN_EXCHANGES)}")
    
    if args.dry_run:
        logger.info("🔍 运行模式: 干运行（不保存结果）")
    
    logger.info("=" * 80)
    
    # 如果是配置检查模式
    if args.config_check:
        if check_config():
            sys.exit(0)
        else:
            sys.exit(1)
    
    try:
        # 运行分析
        asyncio.run(run_analysis_with_options(args))
        
    except KeyboardInterrupt:
        logger.warning("⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序运行出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
