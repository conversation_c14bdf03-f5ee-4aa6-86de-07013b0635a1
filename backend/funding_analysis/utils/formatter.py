#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据格式化器模块

负责格式化输出和数据展示
"""

import sys
from typing import Dict, List
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import Logger
from ..config.settings import AnalyzerConfig

class DataFormatter:
    """数据格式化器类"""
    
    def __init__(self):
        """初始化格式化器"""
        self.logger = Logger.get_logger("data_formatter")
        self.config = AnalyzerConfig()
    
    def print_analysis_summary(self, rate_differences: Dict[str, Dict], stats: Dict) -> None:
        """
        打印分析结果摘要
        
        Args:
            rate_differences: 资金费率差异分析结果
            stats: 统计信息
        """
        self.logger.info("=" * 80)
        self.logger.info("📊 资金费率差异分析结果摘要")
        self.logger.info("=" * 80)
        
        # 统计信息
        self.logger.info(f"📈 总加载合约数: {stats.get('total_contracts_loaded', 0)}")
        self.logger.info(f"🔍 过滤合约数: {stats.get('filtered_contracts', 0)}")
        self.logger.info(f"🎯 不重复合约数: {stats.get('unique_contracts', 0)}")
        self.logger.info(f"⚡ 分析合约数: {stats.get('analyzed_contracts', 0)}")
        self.logger.info(f"💰 发现差异数: {stats.get('saved_differences', 0)}")
        self.logger.info(f"⏱️ 处理时间: {stats.get('processing_time', 0):.2f} 秒")
        
        if rate_differences:
            self.logger.info("\n🔥 前10个最大费率差异:")
            
            # 按费率差异排序
            sorted_diffs = sorted(
                rate_differences.values(),
                key=lambda x: x['rate_diff'],
                reverse=True
            )[:10]
            
            for i, diff in enumerate(sorted_diffs, 1):
                self.logger.info(
                    f"{i:2d}. {diff['base']:8s} | "
                    f"差异: {diff['rate_diff_str']:8s} | "
                    f"最低: {diff['min_exchange']:8s} ({diff['min_rate_str']}) | "
                    f"最高: {diff['max_exchange']:8s} ({diff['max_rate_str']})"
                )
        
        self.logger.info("=" * 80)
    
    def print_detailed_lists(self, detailed_data: Dict) -> None:
        """
        打印详细的过滤、不重复、差异列表
        
        Args:
            detailed_data: 详细数据字典
        """
        self.logger.info("=" * 80)
        self.logger.info("📋 详细数据列表")
        self.logger.info("=" * 80)
        
        # 1. 打印被过滤的合约列表
        filtered_list = detailed_data.get('filtered_contracts_list', [])
        if filtered_list:
            self.logger.info(f"\n🔍 被过滤的合约列表 (共 {len(filtered_list)} 个):")
            self.logger.info("-" * 60)
            
            # 按交易所分组显示
            by_exchange = {}
            for filtered in filtered_list:
                exchange = filtered['exchange']
                if exchange not in by_exchange:
                    by_exchange[exchange] = []
                by_exchange[exchange].append(filtered)
            
            for exchange, contracts in by_exchange.items():
                self.logger.info(f"\n{exchange} (过滤 {len(contracts)} 个):")
                for i, contract in enumerate(contracts[:10], 1):  # 只显示前10个
                    self.logger.info(
                        f"  {i:2d}. {contract['symbol']:20s} | "
                        f"费率: {contract['funding_rate']:>8s} | "
                        f"原因: {contract['reason']}"
                    )
                if len(contracts) > 10:
                    self.logger.info(f"     ... 还有 {len(contracts) - 10} 个合约")
        else:
            self.logger.info("\n🔍 被过滤的合约列表: 无")
        
        # 2. 打印不重复合约列表（按交易所数量分组）
        unique_list = detailed_data.get('unique_contracts_list', [])
        if unique_list:
            self.logger.info(f"\n🎯 不重复合约列表 (共 {len(unique_list)} 个):")
            self.logger.info("-" * 60)
            
            # 按交易所数量分组
            by_exchange_count = {}
            for unique in unique_list:
                count = unique['exchange_count']
                if count not in by_exchange_count:
                    by_exchange_count[count] = []
                by_exchange_count[count].append(unique)
            
            for count in sorted(by_exchange_count.keys(), reverse=True):
                contracts = by_exchange_count[count]
                self.logger.info(f"\n存在于 {count} 个交易所的合约 ({len(contracts)} 个):")
                for i, contract in enumerate(contracts[:15], 1):  # 只显示前15个
                    exchanges_str = ', '.join(contract['exchanges'])
                    self.logger.info(
                        f"  {i:2d}. {contract['symbol']:20s} | "
                        f"交易所: {exchanges_str}"
                    )
                if len(contracts) > 15:
                    self.logger.info(f"     ... 还有 {len(contracts) - 15} 个合约")
        else:
            self.logger.info("\n🎯 不重复合约列表: 无")
        
        # 3. 打印费率差异详细列表
        differences_list = detailed_data.get('differences_list', [])
        if differences_list:
            # 只显示有差异的合约
            with_differences = [d for d in differences_list if d['has_difference']]
            without_differences = [d for d in differences_list if not d['has_difference']]
            
            self.logger.info(f"\n💰 费率差异分析结果:")
            self.logger.info(f"   有差异的合约: {len(with_differences)} 个")
            self.logger.info(f"   无差异的合约: {len(without_differences)} 个")
            self.logger.info("-" * 60)
            
            if with_differences:
                # 按差异大小排序
                sorted_differences = sorted(
                    with_differences,
                    key=lambda x: x.get('rate_diff', 0),
                    reverse=True
                )
                
                self.logger.info(f"\n前20个最大费率差异:")
                for i, diff in enumerate(sorted_differences[:20], 1):
                    symbol = diff['symbol']
                    rate_diff = diff.get('rate_diff', 0)
                    min_exchange = diff.get('min_exchange', 'N/A')
                    max_exchange = diff.get('max_exchange', 'N/A')
                    
                    # 获取所有费率信息
                    rates = diff.get('rates', {})
                    rates_str = ', '.join([f"{ex}:{rate:.4f}%" for ex, rate in rates.items() if rate is not None])
                    
                    self.logger.info(
                        f"  {i:2d}. {symbol:20s} | "
                        f"差异: {rate_diff:.4f}% | "
                        f"最低: {min_exchange:8s} | "
                        f"最高: {max_exchange:8s}"
                    )
                    self.logger.info(f"      费率详情: {rates_str}")
            
            # 显示无差异合约的统计
            if without_differences:
                self.logger.info(f"\n无差异合约统计:")
                
                # 按原因分类
                single_exchange = [d for d in without_differences if len([r for r in d['rates'].values() if r is not None]) == 1]
                same_rates = [d for d in without_differences if len([r for r in d['rates'].values() if r is not None]) > 1]
                
                self.logger.info(f"   只存在于单个交易所: {len(single_exchange)} 个")
                self.logger.info(f"   多个交易所费率相同: {len(same_rates)} 个")
                
                # 显示一些单个交易所的例子
                if single_exchange:
                    self.logger.info(f"\n单个交易所合约示例 (前10个):")
                    for i, contract in enumerate(single_exchange[:10], 1):
                        symbol = contract['symbol']
                        rates = contract['rates']
                        exchange = next(ex for ex, rate in rates.items() if rate is not None)
                        rate = rates[exchange]
                        self.logger.info(f"  {i:2d}. {symbol:20s} | 仅在 {exchange} | 费率: {rate:.4f}%")
        else:
            self.logger.info("\n💰 费率差异分析结果: 无数据")
        
        self.logger.info("=" * 80)
    
    def print_exchange_statistics(self, rate_differences: Dict[str, Dict]) -> None:
        """
        打印交易所统计信息
        
        Args:
            rate_differences: 费率差异数据
        """
        self.logger.info("=" * 80)
        self.logger.info("📈 交易所统计信息")
        self.logger.info("=" * 80)
        
        exchange_stats = {}
        
        for diff in rate_differences.values():
            min_exchange = diff.get('min_exchange')
            max_exchange = diff.get('max_exchange')
            
            if min_exchange:
                if min_exchange not in exchange_stats:
                    exchange_stats[min_exchange] = {'min_count': 0, 'max_count': 0}
                exchange_stats[min_exchange]['min_count'] += 1
            
            if max_exchange:
                if max_exchange not in exchange_stats:
                    exchange_stats[max_exchange] = {'min_count': 0, 'max_count': 0}
                exchange_stats[max_exchange]['max_count'] += 1
        
        for exchange, stats in exchange_stats.items():
            min_count = stats['min_count']
            max_count = stats['max_count']
            total = min_count + max_count
            
            self.logger.info(
                f"{exchange:10s} | "
                f"最低费率次数: {min_count:3d} | "
                f"最高费率次数: {max_count:3d} | "
                f"总计: {total:3d}"
            )
        
        self.logger.info("=" * 80)
    
    def format_for_api(self, rate_differences: Dict[str, Dict], metadata: Dict) -> Dict:
        """
        格式化数据供API使用
        
        Args:
            rate_differences: 费率差异数据
            metadata: 元数据
            
        Returns:
            Dict: API格式的数据
        """
        return {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'data': rate_differences,
            'metadata': metadata,
            'summary': {
                'total_differences': len(rate_differences),
                'analysis_time': metadata.get('analysis_time'),
                'exchanges': metadata.get('exchanges', [])
            }
        }
