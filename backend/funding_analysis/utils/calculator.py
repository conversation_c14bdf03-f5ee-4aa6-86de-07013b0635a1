#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
费率计算器模块

负责计算资金费率差异和相关统计
"""

import sys
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import Logger
from ..config.settings import AnalyzerConfig

class RateCalculator:
    """费率计算器类"""
    
    def __init__(self):
        """初始化计算器"""
        self.logger = Logger.get_logger("rate_calculator")
        self.config = AnalyzerConfig()
    
    def get_contract_funding_rates(self, symbol: str, all_contracts: Dict[str, List[Dict]]) -> Dict[str, Optional[float]]:
        """
        获取指定合约在所有交易所的资金费率
        
        Args:
            symbol: 合约符号
            all_contracts: 所有交易所的合约信息
            
        Returns:
            Dict[str, Optional[float]]: 各交易所的资金费率
        """
        rates = {}
        
        for exchange, contracts in all_contracts.items():
            rate = None
            
            for contract in contracts:
                contract_symbol = contract.get('symbol', '')
                # 这里需要使用过滤器的标准化方法
                from ..core.filter import ContractFilter
                filter_instance = ContractFilter()
                normalized_symbol = filter_instance._normalize_symbol(contract_symbol)
                
                if normalized_symbol == symbol:
                    # 提取资金费率
                    funding_rate_str = contract.get('fundingRate', '0')
                    if isinstance(funding_rate_str, str):
                        # 移除百分号并转换为浮点数
                        rate_str = funding_rate_str.replace('%', '').strip()
                        try:
                            rate = float(rate_str)
                        except ValueError:
                            rate = None
                    elif isinstance(funding_rate_str, (int, float)):
                        rate = float(funding_rate_str)
                    break
            
            rates[exchange] = rate
        
        return rates
    
    def calculate_rate_difference(self, rates: Dict[str, Optional[float]]) -> Optional[Dict]:
        """
        计算资金费率差异
        
        Args:
            rates: 各交易所的资金费率
            
        Returns:
            Optional[Dict]: 费率差异信息，如果不符合条件则返回None
        """
        # 过滤掉None值
        valid_rates = {exchange: rate for exchange, rate in rates.items() if rate is not None}
        
        # 如果交易所数量不足，跳过
        if len(valid_rates) < self.config.ANALYSIS_CONFIG['min_exchanges_count']:
            return None
        
        # 找到最大和最小费率
        min_rate = min(valid_rates.values())
        max_rate = max(valid_rates.values())
        
        # 计算费率差异
        rate_diff = max_rate - min_rate
        
        # 如果费率差异小于阈值，跳过
        if rate_diff < self.config.ANALYSIS_CONFIG['min_rate_difference']:
            return None
        
        # 找到对应的交易所
        min_exchange = next(exchange for exchange, rate in valid_rates.items() if rate == min_rate)
        max_exchange = next(exchange for exchange, rate in valid_rates.items() if rate == max_rate)
        
        # 使用配置中的精度设置
        precision = self.config.OUTPUT_CONFIG['rate_precision']
        use_percentage = self.config.OUTPUT_CONFIG['use_percentage_display']
        
        if use_percentage:
            min_rate_str = f"{min_rate:.{precision}f}%"
            max_rate_str = f"{max_rate:.{precision}f}%"
            rate_diff_str = f"{rate_diff:.{precision}f}%"
        else:
            min_rate_str = f"{min_rate:.{precision}f}"
            max_rate_str = f"{max_rate:.{precision}f}"
            rate_diff_str = f"{rate_diff:.{precision}f}"
        
        return {
            'min_exchange': min_exchange,
            'min_rate': min_rate,
            'min_rate_str': min_rate_str,
            'max_exchange': max_exchange,
            'max_rate': max_rate,
            'max_rate_str': max_rate_str,
            'rate_diff': rate_diff,
            'rate_diff_str': rate_diff_str,
            'exchanges_count': len(valid_rates),
            'all_rates': valid_rates
        }
    
    def analyze_funding_rate_differences(self, unique_contracts: set, all_contracts: Dict[str, List[Dict]]):
        """
        分析所有合约的资金费率差异
        
        Args:
            unique_contracts: 不重复的合约符号集合
            all_contracts: 所有交易所的合约信息
            
        Returns:
            Tuple[Dict[str, Dict], List[Dict]]: (资金费率差异分析结果, 详细分析列表)
        """
        self.logger.info("开始分析资金费率差异...")
        
        rate_differences = {}
        analyzed_count = 0
        saved_count = 0
        differences_list = []  # 记录所有差异详情
        
        total_contracts = len(unique_contracts)
        
        for i, symbol in enumerate(unique_contracts, 1):
            if i % self.config.ANALYSIS_CONFIG['progress_report_interval'] == 0 or i == total_contracts:
                self.logger.info(f"分析进度: {i}/{total_contracts} ({i/total_contracts*100:.1f}%)")
            
            # 获取该合约在所有交易所的资金费率
            rates = self.get_contract_funding_rates(symbol, all_contracts)
            analyzed_count += 1
            
            # 计算费率差异
            difference = self.calculate_rate_difference(rates)
            
            # 记录分析详情（无论是否有差异）
            analysis_detail = {
                'symbol': symbol,
                'base_currency': symbol.split('/')[0] if '/' in symbol else symbol,
                'rates': rates,
                'has_difference': difference is not None,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            if difference:
                # 提取基础货币名称（如BTC/USDT -> BTC）
                base_currency = symbol.split('/')[0] if '/' in symbol else symbol
                
                rate_differences[base_currency] = {
                    'base': base_currency,
                    'symbol': symbol,
                    **difference,
                    'timestamp': datetime.now().isoformat()
                }
                
                # 添加差异信息到详情
                analysis_detail.update({
                    'difference_info': difference,
                    'rate_diff': difference['rate_diff'],
                    'min_exchange': difference['min_exchange'],
                    'max_exchange': difference['max_exchange']
                })
                
                saved_count += 1
            
            differences_list.append(analysis_detail)
        
        self.logger.success(
            f"分析完成！分析了 {analyzed_count} 个合约，"
            f"发现 {saved_count} 个有意义的费率差异"
        )
        
        return rate_differences, differences_list
    
    def calculate_statistics(self, rate_differences: Dict[str, Dict]) -> Dict:
        """
        计算分析统计信息
        
        Args:
            rate_differences: 费率差异数据
            
        Returns:
            Dict: 统计信息
        """
        if not rate_differences:
            return {
                'total_differences': 0,
                'max_difference': 0,
                'min_difference': 0,
                'avg_difference': 0,
                'exchange_stats': {}
            }
        
        differences = [diff['rate_diff'] for diff in rate_differences.values()]
        
        # 交易所统计
        exchange_stats = {}
        for diff in rate_differences.values():
            min_ex = diff['min_exchange']
            max_ex = diff['max_exchange']
            
            if min_ex not in exchange_stats:
                exchange_stats[min_ex] = {'min_count': 0, 'max_count': 0}
            if max_ex not in exchange_stats:
                exchange_stats[max_ex] = {'min_count': 0, 'max_count': 0}
            
            exchange_stats[min_ex]['min_count'] += 1
            exchange_stats[max_ex]['max_count'] += 1
        
        return {
            'total_differences': len(differences),
            'max_difference': max(differences),
            'min_difference': min(differences),
            'avg_difference': sum(differences) / len(differences),
            'exchange_stats': exchange_stats
        }
