# 资金费率差异分析器

一个专业的加密货币交易所资金费率差异分析工具，用于识别不同交易所间的套利机会。

## 📁 项目结构

```
funding_analysis/
├── __init__.py                 # 模块初始化文件
├── README.md                   # 项目文档
├── config/                     # 配置模块
│   ├── __init__.py
│   └── settings.py            # 配置设置
├── core/                      # 核心功能模块
│   ├── __init__.py
│   ├── analyzer.py            # 主分析器
│   ├── data_loader.py         # 数据加载器
│   └── filter.py              # 合约过滤器
├── utils/                     # 工具模块
│   ├── __init__.py
│   ├── calculator.py          # 费率计算器
│   └── formatter.py           # 数据格式化器
└── scripts/                   # 脚本模块
    ├── __init__.py
    ├── run_analyzer.py        # 运行脚本
    └── query_results.py       # 查询脚本
```

## 🚀 快速开始

### 基本使用

```bash
# 运行完整分析
python funding_analysis/scripts/run_analyzer.py

# 干运行模式（测试用）
python funding_analysis/scripts/run_analyzer.py --dry-run

# 查询分析结果
python funding_analysis/scripts/query_results.py
```

### 高级选项

```bash
# 指定交易所
python funding_analysis/scripts/run_analyzer.py --exchanges binance okx

# 自定义费率差异阈值
python funding_analysis/scripts/run_analyzer.py --min-rate-diff 0.001

# 详细输出模式
python funding_analysis/scripts/run_analyzer.py --verbose

# 检查配置
python funding_analysis/scripts/run_analyzer.py --config-check
```

## 🔧 核心功能

### 1. 数据加载 (DataLoader)
- 从Redis加载各交易所的合约信息
- 支持多交易所并行加载
- 数据验证和统计

### 2. 合约过滤 (ContractFilter)
- 过滤100xx开头的合约
- 符号标准化处理
- 不重复合约集合计算

### 3. 费率计算 (RateCalculator)
- 多交易所费率比较
- 差异阈值筛选
- 统计信息计算

### 4. 数据格式化 (DataFormatter)
- 结果展示和格式化
- 详细列表输出
- API格式转换

### 5. 主分析器 (FundingRateAnalyzer)
- 协调各组件工作
- 完整分析流程
- Redis数据存储

## 📊 分析结果

### 输出内容
- **统计摘要**: 总合约数、过滤数、差异数等
- **被过滤合约**: 详细的过滤列表和原因
- **不重复合约**: 按交易所数量分组的合约分布
- **费率差异**: 最大差异排序和详细费率信息
- **交易所统计**: 各交易所的最高/最低费率次数

### 示例输出
```
📊 资金费率差异分析结果摘要
📈 总加载合约数: 1,104
🔍 过滤合约数: 32
🎯 不重复合约数: 526
💰 发现差异数: 174
⏱️ 处理时间: 0.25秒

🔥 前5个最大费率差异:
1. BR/USDT    | 差异: 0.0014% | bybit (-0.0040%) → binance (-0.0026%)
2. MOVE/USDT  | 差异: 0.0010% | binance (-0.0019%) → bybit (-0.0009%)
3. SPX/USDT   | 差异: 0.0008% | binance (-0.0009%) → bybit (-0.0001%)
```

## ⚙️ 配置说明

### 主要配置项

```python
# 交易所配置
MAIN_EXCHANGES = ['binance', 'okx', 'bybit']

# 过滤配置
CONTRACT_FILTER_CONFIG = {
    'filter_patterns': [r'^100\d+', r'^1000+'],
    'quote_currencies': ['USDT', 'USDC', 'USD']
}

# 分析配置
ANALYSIS_CONFIG = {
    'min_rate_difference': 0.0001,  # 最小差异阈值
    'min_exchanges_count': 2,       # 最小交易所数量
    'progress_report_interval': 100  # 进度报告间隔
}
```

### Redis键说明

所有分析结果存储在Redis中，使用以下键：

- `funding_analysis:unique_contracts` - 不重复合约集合
- `funding_analysis:rate_differences` - 费率差异数据
- `funding_analysis:analysis_results` - 完整分析结果
- `funding_analysis:metadata` - 分析元数据
- `funding_analysis:filtered_contracts` - 过滤结果
- `funding_analysis:detailed_output` - 详细输出数据

## 🔍 API使用

### 程序化调用

```python
from funding_analysis import FundingRateAnalyzer

# 创建分析器实例
analyzer = FundingRateAnalyzer()

# 运行分析
await analyzer.run_analysis()

# 获取结果
results = await analyzer.redis_store.get('funding_analysis:analysis_results')
```

### 自定义配置

```python
from funding_analysis.config.settings import AnalyzerConfig

# 修改配置
config = AnalyzerConfig()
config.MAIN_EXCHANGES = ['binance', 'okx']
config.ANALYSIS_CONFIG['min_rate_difference'] = 0.001

# 使用自定义配置
analyzer = FundingRateAnalyzer()
analyzer.config = config
```

## 📈 性能特性

- **高效处理**: 0.25秒分析526个合约
- **内存优化**: 流式处理大量数据
- **并发加载**: 多交易所并行数据获取
- **增量更新**: 支持增量分析模式

## 🛠️ 扩展性

### 添加新交易所
1. 在配置中添加交易所名称
2. 确保Redis中有对应的合约数据
3. 运行分析器即可

### 自定义过滤规则
```python
# 在配置中添加新的过滤模式
config.CONTRACT_FILTER_CONFIG['filter_patterns'].append(r'^CUSTOM_')
```

### 自定义输出格式
```python
# 继承DataFormatter类
class CustomFormatter(DataFormatter):
    def custom_output(self, data):
        # 自定义输出逻辑
        pass
```

## 🔧 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否运行
   - 验证连接配置

2. **数据加载失败**
   - 确认交易所数据已写入Redis
   - 检查数据格式是否正确

3. **分析结果为空**
   - 调整费率差异阈值
   - 检查合约过滤规则

### 调试模式

```bash
# 启用详细日志
python funding_analysis/scripts/run_analyzer.py --verbose

# 检查配置
python funding_analysis/scripts/run_analyzer.py --config-check
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持三大交易所分析
- 完整的过滤和分析功能
- 详细的输出和统计
