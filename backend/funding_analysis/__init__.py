#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
资金费率差异分析模块

这个模块提供了完整的资金费率差异分析功能，包括：
- 从Redis加载各交易所的合约信息
- 过滤和标准化合约符号
- 计算不同交易所间的资金费率差异
- 保存分析结果到Redis

主要组件：
- core.analyzer: 核心分析器
- core.data_loader: 数据加载器
- core.filter: 合约过滤器
- utils.calculator: 费率计算工具
- utils.formatter: 数据格式化工具
- config.settings: 配置管理
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入主要类和函数
from .core.analyzer import FundingRateAnalyzer
from .core.data_loader import DataLoader
from .core.filter import ContractFilter
from .utils.calculator import RateCalculator
from .utils.formatter import DataFormatter
from .config.settings import AnalyzerConfig

__all__ = [
    'FundingRateAnalyzer',
    'DataLoader', 
    'ContractFilter',
    'RateCalculator',
    'DataFormatter',
    'AnalyzerConfig'
]
