#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据加载器模块

负责从Redis加载各交易所的合约信息
"""

import sys
import os
from typing import Dict, List
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config.settings import REDIS_URL
from store.redis_store import RedisStore
from utils.logger import Logger, log_error

class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        """初始化数据加载器"""
        self.logger = Logger.get_logger("data_loader")
        
    @log_error
    async def load_exchange_contracts(self, exchange_name: str) -> List[Dict]:
        """
        从Redis加载指定交易所的合约信息

        Args:
            exchange_name: 交易所名称

        Returns:
            List[Dict]: 合约信息列表
        """
        try:
            self.logger.info(f"📥 正在从Redis加载 {exchange_name} 的合约信息...")

            # 创建交易所特定的RedisStore实例
            exchange_store = RedisStore(REDIS_URL, exchange_name)
            contracts = exchange_store.get_exchange_contracts()

            # 记录详细的加载信息
            self.logger.info(f"📊 {exchange_name} 加载详情:")
            self.logger.info(f"  合约数量: {len(contracts)}")

            if contracts:
                # 分析合约数据结构
                sample_contract = contracts[0]
                self.logger.info(f"  数据字段: {', '.join(sample_contract.keys())}")

                # 统计有资金费率的合约
                with_funding_rate = sum(1 for c in contracts if c.get('fundingRate') is not None)
                self.logger.info(f"  有资金费率的合约: {with_funding_rate}/{len(contracts)}")

                # 显示前3个合约作为示例
                self.logger.info(f"  合约示例 (前3个):")
                for i, contract in enumerate(contracts[:3], 1):
                    symbol = contract.get('symbol', 'N/A')
                    funding_rate = contract.get('fundingRate', 'N/A')
                    self.logger.info(f"    {i}. {symbol:20s} | 费率: {funding_rate}")

            self.logger.success(f"✅ 成功加载 {exchange_name} 的 {len(contracts)} 个合约")
            return contracts

        except Exception as e:
            self.logger.error(f"❌ 加载 {exchange_name} 合约信息失败: {str(e)}")
            return []
    
    @log_error
    async def load_all_contracts(self, exchanges: List[str]) -> Dict[str, List[Dict]]:
        """
        加载所有指定交易所的合约信息

        Args:
            exchanges: 交易所列表

        Returns:
            Dict[str, List[Dict]]: 按交易所分组的合约信息
        """
        self.logger.info("🚀 开始加载所有交易所的合约信息...")
        self.logger.info(f"🎯 目标交易所: {', '.join(exchanges)}")

        all_contracts = {}
        total_loaded = 0
        loading_stats = {}

        for i, exchange in enumerate(exchanges, 1):
            self.logger.info(f"📥 [{i}/{len(exchanges)}] 加载 {exchange}...")
            contracts = await self.load_exchange_contracts(exchange)
            all_contracts[exchange] = contracts
            total_loaded += len(contracts)

            # 记录每个交易所的统计信息
            loading_stats[exchange] = {
                'count': len(contracts),
                'has_funding_rate': sum(1 for c in contracts if c.get('fundingRate') is not None),
                'success': len(contracts) > 0
            }

        # 打印加载总结
        self.logger.success(f"🎉 所有交易所合约加载完成!")
        self.logger.info(f"📊 加载总结:")
        self.logger.info(f"  总合约数: {total_loaded}")
        self.logger.info(f"  交易所数: {len(exchanges)}")

        # 详细统计
        self.logger.info(f"📈 各交易所详情:")
        for exchange, stats in loading_stats.items():
            status = "✅" if stats['success'] else "❌"
            self.logger.info(
                f"  {status} {exchange:10s}: {stats['count']:4d}个合约 "
                f"(有费率: {stats['has_funding_rate']:4d}个)"
            )

        return all_contracts
    
    def get_loading_statistics(self, all_contracts: Dict[str, List[Dict]]) -> Dict:
        """
        获取加载统计信息
        
        Args:
            all_contracts: 所有交易所的合约信息
            
        Returns:
            Dict: 统计信息
        """
        stats = {
            'total_contracts': 0,
            'exchanges_count': len(all_contracts),
            'by_exchange': {}
        }
        
        for exchange, contracts in all_contracts.items():
            count = len(contracts)
            stats['by_exchange'][exchange] = count
            stats['total_contracts'] += count
        
        return stats
    
    def validate_loaded_data(self, all_contracts: Dict[str, List[Dict]]) -> bool:
        """
        验证加载的数据
        
        Args:
            all_contracts: 所有交易所的合约信息
            
        Returns:
            bool: 数据是否有效
        """
        try:
            if not all_contracts:
                self.logger.warning("没有加载到任何合约数据")
                return False
            
            total_contracts = 0
            for exchange, contracts in all_contracts.items():
                if not isinstance(contracts, list):
                    self.logger.error(f"{exchange} 的合约数据格式错误")
                    return False
                
                total_contracts += len(contracts)
                
                # 验证合约数据结构
                for contract in contracts[:5]:  # 只验证前5个
                    if not isinstance(contract, dict):
                        self.logger.error(f"{exchange} 的合约数据不是字典格式")
                        return False
                    
                    if 'symbol' not in contract:
                        self.logger.error(f"{exchange} 的合约缺少symbol字段")
                        return False
            
            if total_contracts == 0:
                self.logger.warning("所有交易所的合约数据都为空")
                return False
            
            self.logger.success(f"数据验证通过，总共 {total_contracts} 个合约")
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
