#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
合约过滤器模块

负责过滤和标准化合约符号
"""

import re
import sys
from typing import Dict, List, Set, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import Logger
from ..config.settings import AnalyzerConfig

class ContractFilter:
    """合约过滤器类"""
    
    def __init__(self):
        """初始化过滤器"""
        self.logger = Logger.get_logger("contract_filter")
        self.config = AnalyzerConfig()
        
        # 统计信息
        self.filter_stats = {
            'total_processed': 0,
            'total_filtered': 0,
            'by_exchange': {},
            'filtered_contracts': []
        }
    
    def _is_filtered_contract(self, symbol: str) -> bool:
        """
        检查合约是否需要被过滤
        
        Args:
            symbol: 合约符号
            
        Returns:
            bool: True表示需要过滤，False表示保留
        """
        # 使用配置文件中的过滤规则
        for pattern in self.config.CONTRACT_FILTER_CONFIG['filter_patterns']:
            if re.match(pattern, symbol):
                return True
        return False
    
    def _normalize_symbol(self, symbol: str) -> str:
        """
        标准化合约符号
        
        Args:
            symbol: 原始合约符号
            
        Returns:
            str: 标准化后的合约符号
        """
        # 移除常见的后缀变体，统一格式
        symbol = symbol.upper()
        
        # 使用配置文件中的标准化规则
        for old, new in self.config.CONTRACT_FILTER_CONFIG['normalization_rules']:
            symbol = symbol.replace(old, new)
        
        # 确保使用/分隔符处理支持的报价货币
        for quote_currency in self.config.CONTRACT_FILTER_CONFIG['quote_currencies']:
            if quote_currency in symbol and '/' not in symbol:
                symbol = symbol.replace(quote_currency, f'/{quote_currency}')
        
        return symbol
    
    def filter_and_normalize_contracts(self, all_contracts: Dict[str, List[Dict]]) -> Tuple[Dict[str, Set[str]], List[Dict]]:
        """
        过滤并标准化合约符号
        
        Args:
            all_contracts: 所有交易所的合约信息
            
        Returns:
            Tuple[Dict[str, Set[str]], List[Dict]]: (按交易所分组的标准化合约符号集合, 被过滤的合约列表)
        """
        self.logger.info("开始过滤和标准化合约符号...")
        
        filtered_contracts = {}
        filtered_list = []  # 记录被过滤的合约
        
        for exchange, contracts in all_contracts.items():
            exchange_symbols = set()
            filtered_count = 0
            exchange_filtered = []
            
            self.filter_stats['by_exchange'][exchange] = {
                'total': len(contracts),
                'filtered': 0,
                'kept': 0
            }
            
            for contract in contracts:
                symbol = contract.get('symbol', '')
                if not symbol:
                    continue
                
                self.filter_stats['total_processed'] += 1
                
                # 检查是否需要过滤
                if self._is_filtered_contract(symbol):
                    filtered_count += 1
                    self.filter_stats['total_filtered'] += 1
                    
                    filtered_info = {
                        'symbol': symbol,
                        'exchange': exchange,
                        'reason': '匹配过滤规则',
                        'funding_rate': contract.get('fundingRate', 'N/A')
                    }
                    exchange_filtered.append(filtered_info)
                    filtered_list.append(filtered_info)
                    continue
                
                # 标准化符号
                normalized_symbol = self._normalize_symbol(symbol)
                exchange_symbols.add(normalized_symbol)
            
            filtered_contracts[exchange] = exchange_symbols
            self.filter_stats['by_exchange'][exchange]['filtered'] = filtered_count
            self.filter_stats['by_exchange'][exchange]['kept'] = len(exchange_symbols)
            
            self.logger.info(
                f"{exchange}: 保留 {len(exchange_symbols)} 个合约, "
                f"过滤 {filtered_count} 个合约"
            )
            
            # 打印被过滤的合约（如果有的话）
            if exchange_filtered:
                self.logger.info(f"{exchange} 被过滤的合约:")
                for filtered in exchange_filtered[:5]:  # 只显示前5个
                    self.logger.info(f"  - {filtered['symbol']} (费率: {filtered['funding_rate']})")
                if len(exchange_filtered) > 5:
                    self.logger.info(f"  ... 还有 {len(exchange_filtered) - 5} 个合约被过滤")
        
        # 保存过滤列表到统计信息
        self.filter_stats['filtered_contracts'] = filtered_list
        self.logger.success(f"过滤完成，总共过滤了 {self.filter_stats['total_filtered']} 个合约")
        
        return filtered_contracts, filtered_list
    
    def get_unique_contracts(self, filtered_contracts: Dict[str, Set[str]]) -> Tuple[Set[str], List[Dict]]:
        """
        获取所有交易所的不重复合约集合
        
        Args:
            filtered_contracts: 按交易所分组的合约符号集合
            
        Returns:
            Tuple[Set[str], List[Dict]]: (不重复的合约符号集合, 详细的不重复合约列表)
        """
        self.logger.info("正在计算不重复合约集合...")
        
        unique_contracts = set()
        contract_exchange_mapping = {}  # 记录每个合约在哪些交易所存在
        
        for exchange, symbols in filtered_contracts.items():
            for symbol in symbols:
                unique_contracts.add(symbol)
                if symbol not in contract_exchange_mapping:
                    contract_exchange_mapping[symbol] = []
                contract_exchange_mapping[symbol].append(exchange)
        
        # 创建详细的不重复合约列表
        unique_list = []
        for symbol in sorted(unique_contracts):
            exchanges = contract_exchange_mapping[symbol]
            unique_info = {
                'symbol': symbol,
                'base_currency': symbol.split('/')[0] if '/' in symbol else symbol,
                'exchanges': exchanges,
                'exchange_count': len(exchanges)
            }
            unique_list.append(unique_info)
        
        # 打印统计信息
        self.logger.success(f"找到 {len(unique_contracts)} 个不重复的合约")
        
        # 按交易所数量分组统计
        exchange_count_stats = {}
        for info in unique_list:
            count = info['exchange_count']
            if count not in exchange_count_stats:
                exchange_count_stats[count] = 0
            exchange_count_stats[count] += 1
        
        self.logger.info("合约分布统计:")
        for count in sorted(exchange_count_stats.keys()):
            self.logger.info(f"  存在于 {count} 个交易所的合约: {exchange_count_stats[count]} 个")
        
        return unique_contracts, unique_list
    
    def get_filter_statistics(self) -> Dict:
        """
        获取过滤统计信息
        
        Returns:
            Dict: 过滤统计信息
        """
        return self.filter_stats.copy()
    
    def print_filter_summary(self) -> None:
        """打印过滤摘要"""
        self.logger.info("=" * 60)
        self.logger.info("🔍 合约过滤摘要")
        self.logger.info("=" * 60)
        
        self.logger.info(f"📊 总处理合约数: {self.filter_stats['total_processed']}")
        self.logger.info(f"🚫 总过滤合约数: {self.filter_stats['total_filtered']}")
        self.logger.info(f"✅ 总保留合约数: {self.filter_stats['total_processed'] - self.filter_stats['total_filtered']}")
        
        self.logger.info("\n📈 各交易所统计:")
        for exchange, stats in self.filter_stats['by_exchange'].items():
            self.logger.info(
                f"  {exchange:10s}: 总计 {stats['total']:4d} | "
                f"保留 {stats['kept']:4d} | 过滤 {stats['filtered']:4d}"
            )
        
        self.logger.info("=" * 60)
