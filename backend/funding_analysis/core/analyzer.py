#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
资金费率差异分析器核心模块

主要的分析器类，协调各个组件完成分析任务
"""

import json
import sys
from typing import Dict, List
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config.settings import REDIS_URL
from store.redis_store import RedisStore
from utils.logger import Logger, log_success, log_error
from utils.monitor import PerformanceMonitor

from .data_loader import DataLoader
from .filter import ContractFilter
from ..utils.calculator import RateCalculator
from ..utils.formatter import DataFormatter
from ..config.settings import AnalyzerConfig

class FundingRateAnalyzer:
    """资金费率差异分析器主类"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = Logger.get_logger("funding_rate_analyzer")
        self.config = AnalyzerConfig()
        self.redis_store = RedisStore(REDIS_URL, "analyzer")
        self.performance_monitor = PerformanceMonitor()
        
        # 初始化各个组件
        self.data_loader = DataLoader()
        self.contract_filter = ContractFilter()
        self.rate_calculator = RateCalculator()
        self.data_formatter = DataFormatter()
        
        # 统计信息
        self.stats = {
            'total_contracts_loaded': 0,
            'filtered_contracts': 0,
            'unique_contracts': 0,
            'analyzed_contracts': 0,
            'saved_differences': 0,
            'processing_time': 0.0
        }
        
        # 详细数据存储
        self.detailed_data = {
            'filtered_contracts_list': [],  # 被过滤的合约列表
            'unique_contracts_list': [],    # 不重复合约列表
            'differences_list': []          # 费率差异列表
        }
    
    @log_error
    async def save_unique_contracts(self, unique_contracts: set) -> None:
        """
        保存不重复合约集合到Redis (使用SET数据结构)

        Args:
            unique_contracts: 不重复的合约符号集合
        """
        try:
            self.logger.info("正在保存不重复合约集合到Redis...")

            contracts_list = list(unique_contracts)
            redis_key = self.config.REDIS_KEYS['unique_contracts']

            # 使用SET数据结构存储合约列表
            self.logger.info(f"保存详情:")
            self.logger.info(f"  Redis键: {redis_key}")
            self.logger.info(f"  数据结构: SET")
            self.logger.info(f"  合约数量: {len(contracts_list)}")
            self.logger.info(f"  涉及交易所: {', '.join(self.config.MAIN_EXCHANGES)}")

            # 清空现有的SET并添加新数据
            self.redis_store.delete(redis_key)
            if contracts_list:
                self.redis_store.sadd(redis_key, *contracts_list)

            # 设置过期时间 (24小时)
            self.redis_store.expire(redis_key, 86400)

            # 保存元数据到单独的键
            metadata_key = f"{redis_key}:metadata"
            metadata = {
                'count': len(contracts_list),
                'timestamp': datetime.now().isoformat(),
                'exchanges': self.config.MAIN_EXCHANGES,
                'data_type': 'unique_contracts_set'
            }
            await self.redis_store.set(metadata_key, json.dumps(metadata))
            self.redis_store.expire(metadata_key, 86400)

            # 显示前10个合约作为示例
            if contracts_list:
                self.logger.info(f"  合约示例 (前10个):")
                for i, contract in enumerate(sorted(contracts_list)[:10], 1):
                    self.logger.info(f"    {i:2d}. {contract}")
                if len(contracts_list) > 10:
                    self.logger.info(f"    ... 还有 {len(contracts_list) - 10} 个合约")

            self.logger.success(f"✅ 成功保存 {len(contracts_list)} 个不重复合约到Redis SET")
            self.logger.info(f"📍 主键: {redis_key} (SET)")
            self.logger.info(f"📍 元数据键: {metadata_key} (STRING)")

        except Exception as e:
            self.logger.error(f"保存不重复合约集合失败: {str(e)}")
            raise
    
    @log_error
    async def save_analysis_results(self, rate_differences: Dict[str, Dict]) -> None:
        """
        保存分析结果到Redis (使用HASH数据结构)

        Args:
            rate_differences: 资金费率差异分析结果
        """
        try:
            self.logger.info("正在保存分析结果到Redis...")

            # 1. 保存费率差异数据 (使用HASH)
            rate_diff_key = self.config.REDIS_KEYS['rate_differences']

            self.logger.info(f"💰 保存费率差异数据:")
            self.logger.info(f"  Redis键: {rate_diff_key}")
            self.logger.info(f"  数据结构: HASH")
            self.logger.info(f"  差异数量: {len(rate_differences)}")

            # 清空现有的HASH
            self.redis_store.delete(rate_diff_key)

            # 准备HASH数据
            hash_data = {}
            total_size = 0

            for base_currency, diff_data in rate_differences.items():
                # 为每个差异创建一个字段
                field_data = json.dumps(diff_data)
                hash_data[base_currency] = field_data
                total_size += len(field_data)

            # 批量设置HASH字段
            if hash_data:
                self.redis_store.hset_multiple(rate_diff_key, hash_data)

            # 设置过期时间 (24小时)
            self.redis_store.expire(rate_diff_key, 86400)

            self.logger.info(f"  总数据大小: {total_size} 字节")

            # 显示前5个最大差异作为示例
            if rate_differences:
                sorted_diffs = sorted(
                    rate_differences.items(),
                    key=lambda x: x[1].get('rate_diff', 0),
                    reverse=True
                )[:5]

                self.logger.info(f"  前5个最大差异:")
                for i, (base, diff) in enumerate(sorted_diffs, 1):
                    symbol = diff.get('symbol', base)
                    rate_diff = diff.get('rate_diff_str', 'N/A')
                    min_ex = diff.get('min_exchange', 'N/A')
                    max_ex = diff.get('max_exchange', 'N/A')
                    self.logger.info(f"    {i}. {symbol:15s} | 差异: {rate_diff:8s} | {min_ex} → {max_ex}")

            self.logger.success(f"✅ 费率差异数据保存完成 (HASH)")
            self.logger.info(f"📍 Redis键: {rate_diff_key} (HASH, {len(hash_data)} 字段)")

            # 2. 保存分析元数据 (使用STRING)
            metadata = {
                'analysis_time': datetime.now().isoformat(),
                'exchanges': self.config.MAIN_EXCHANGES,
                'total_differences': len(rate_differences),
                'statistics': self.stats
            }

            metadata_key = self.config.REDIS_KEYS['analysis_metadata']
            metadata_data = json.dumps(metadata)
            metadata_size = len(metadata_data)

            self.logger.info(f"📊 保存分析元数据:")
            self.logger.info(f"  Redis键: {metadata_key}")
            self.logger.info(f"  数据结构: STRING")
            self.logger.info(f"  数据大小: {metadata_size} 字节")
            self.logger.info(f"  统计信息: 加载{self.stats['total_contracts_loaded']}个, 过滤{self.stats['filtered_contracts']}个, 分析{self.stats['analyzed_contracts']}个")

            await self.redis_store.set(metadata_key, metadata_data, ttl=86400)
            self.logger.success(f"✅ 分析元数据保存完成")

            # 3. 保存完整的分析结果（用于API接口，使用STRING）
            analysis_results = self.data_formatter.format_for_api(rate_differences, metadata)

            results_key = self.config.REDIS_KEYS['analysis_results']
            results_data = json.dumps(analysis_results)
            results_size = len(results_data)

            self.logger.info(f"🔗 保存API格式结果:")
            self.logger.info(f"  Redis键: {results_key}")
            self.logger.info(f"  数据结构: STRING")
            self.logger.info(f"  数据大小: {results_size} 字节")
            self.logger.info(f"  包含字段: success, timestamp, data, metadata, summary")

            await self.redis_store.set(results_key, results_data, ttl=86400)
            self.logger.success(f"✅ API格式结果保存完成")

            # 总结
            total_size = total_size + metadata_size + results_size
            self.logger.success(f"🎉 分析结果保存完成!")
            self.logger.info(f"📈 保存总结:")
            self.logger.info(f"  保存的Redis键数量: 3个")
            self.logger.info(f"  HASH键: {rate_diff_key} ({len(rate_differences)} 字段)")
            self.logger.info(f"  STRING键: {metadata_key}, {results_key}")
            self.logger.info(f"  总数据大小: {total_size} 字节 ({total_size/1024:.1f} KB)")
            self.logger.info(f"  费率差异数量: {len(rate_differences)}个")

        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")
            raise
    
    @log_error
    async def save_detailed_output(self) -> None:
        """
        保存详细输出数据到Redis (使用LIST和STRING数据结构)
        """
        try:
            self.logger.info("正在保存详细输出数据到Redis...")

            # 1. 保存过滤结果 (使用STRING)
            filtered_data = {
                'filtered_contracts': self.detailed_data['filtered_contracts_list'],
                'filter_stats': {
                    'total_filtered': len(self.detailed_data['filtered_contracts_list']),
                    'filter_by_exchange': {}
                },
                'timestamp': datetime.now().isoformat()
            }

            # 统计各交易所过滤数量
            for filtered in self.detailed_data['filtered_contracts_list']:
                exchange = filtered['exchange']
                if exchange not in filtered_data['filter_stats']['filter_by_exchange']:
                    filtered_data['filter_stats']['filter_by_exchange'][exchange] = 0
                filtered_data['filter_stats']['filter_by_exchange'][exchange] += 1

            filtered_key = self.config.REDIS_KEYS['filtered_contracts']
            filtered_json = json.dumps(filtered_data)
            filtered_size = len(filtered_json)

            self.logger.info(f"🔍 保存过滤结果数据:")
            self.logger.info(f"  Redis键: {filtered_key}")
            self.logger.info(f"  数据结构: STRING")
            self.logger.info(f"  过滤合约数: {len(self.detailed_data['filtered_contracts_list'])}")
            self.logger.info(f"  数据大小: {filtered_size} 字节")

            # 显示各交易所过滤统计
            if filtered_data['filter_stats']['filter_by_exchange']:
                self.logger.info(f"  各交易所过滤统计:")
                for exchange, count in filtered_data['filter_stats']['filter_by_exchange'].items():
                    self.logger.info(f"    {exchange}: {count}个")

            await self.redis_store.set(filtered_key, filtered_json, ttl=86400)
            self.logger.success(f"✅ 过滤结果数据保存完成")

            # 2. 保存详细输出数据
            detailed_output = {
                'filtered_contracts': self.detailed_data['filtered_contracts_list'],
                'unique_contracts': self.detailed_data['unique_contracts_list'],
                'differences_analysis': self.detailed_data['differences_list'],
                'summary': {
                    'total_filtered': len(self.detailed_data['filtered_contracts_list']),
                    'total_unique': len(self.detailed_data['unique_contracts_list']),
                    'total_analyzed': len(self.detailed_data['differences_list']),
                    'total_with_differences': len([d for d in self.detailed_data['differences_list'] if d['has_difference']])
                },
                'timestamp': datetime.now().isoformat()
            }

            detailed_key = self.config.REDIS_KEYS['detailed_output']
            detailed_json = json.dumps(detailed_output)
            detailed_size = len(detailed_json)

            self.logger.info(f"📋 保存详细输出数据:")
            self.logger.info(f"  Redis键: {detailed_key}")
            self.logger.info(f"  数据大小: {detailed_size} 字节")
            self.logger.info(f"  包含数据:")
            self.logger.info(f"    - 过滤合约: {len(self.detailed_data['filtered_contracts_list'])}个")
            self.logger.info(f"    - 不重复合约: {len(self.detailed_data['unique_contracts_list'])}个")
            self.logger.info(f"    - 分析记录: {len(self.detailed_data['differences_list'])}个")
            self.logger.info(f"    - 有差异记录: {detailed_output['summary']['total_with_differences']}个")

            await self.redis_store.set(detailed_key, detailed_json)
            self.logger.success(f"✅ 详细输出数据保存完成")

            # 总结
            total_size = filtered_size + detailed_size
            self.logger.success(f"🎉 详细输出保存完成!")
            self.logger.info(f"📈 保存总结:")
            self.logger.info(f"  保存的Redis键数量: 2个")
            self.logger.info(f"  总数据大小: {total_size} 字节 ({total_size/1024:.1f} KB)")
            self.logger.info(f"  详细数据记录总数: {len(self.detailed_data['filtered_contracts_list']) + len(self.detailed_data['unique_contracts_list']) + len(self.detailed_data['differences_list'])}个")

        except Exception as e:
            self.logger.error(f"保存详细输出数据失败: {str(e)}")
            raise
    
    @log_success
    async def run_analysis(self) -> None:
        """
        运行完整的资金费率差异分析流程
        """
        self.logger.info("🚀 开始资金费率差异分析...")
        self.performance_monitor.start()
        
        try:
            # 1. 加载所有交易所的合约信息
            all_contracts = await self.data_loader.load_all_contracts(self.config.MAIN_EXCHANGES)
            loading_stats = self.data_loader.get_loading_statistics(all_contracts)
            self.stats['total_contracts_loaded'] = loading_stats['total_contracts']
            
            # 验证加载的数据
            if not self.data_loader.validate_loaded_data(all_contracts):
                raise ValueError("加载的数据验证失败")
            
            # 2. 过滤并标准化合约符号
            filtered_contracts, filtered_list = self.contract_filter.filter_and_normalize_contracts(all_contracts)
            self.detailed_data['filtered_contracts_list'] = filtered_list
            filter_stats = self.contract_filter.get_filter_statistics()
            self.stats['filtered_contracts'] = filter_stats['total_filtered']
            
            # 3. 获取不重复合约集合
            unique_contracts, unique_list = self.contract_filter.get_unique_contracts(filtered_contracts)
            self.detailed_data['unique_contracts_list'] = unique_list
            self.stats['unique_contracts'] = len(unique_contracts)
            
            # 4. 保存不重复合约集合到Redis
            await self.save_unique_contracts(unique_contracts)
            
            # 5. 分析资金费率差异
            rate_differences, differences_list = self.rate_calculator.analyze_funding_rate_differences(unique_contracts, all_contracts)
            self.detailed_data['differences_list'] = differences_list
            self.stats['analyzed_contracts'] = len(differences_list)
            self.stats['saved_differences'] = len(rate_differences)
            
            # 6. 保存分析结果到Redis
            await self.save_analysis_results(rate_differences)
            
            # 7. 保存详细输出数据到Redis
            await self.save_detailed_output()
            
            # 8. 记录处理时间
            self.stats['processing_time'] = self.performance_monitor.end()
            
            # 9. 打印分析摘要
            self.data_formatter.print_analysis_summary(rate_differences, self.stats)
            
            # 10. 打印详细列表
            self.data_formatter.print_detailed_lists(self.detailed_data)
            
            # 11. 打印交易所统计
            self.data_formatter.print_exchange_statistics(rate_differences)
            
            self.logger.success("✅ 资金费率差异分析完成！")
            
        except Exception as e:
            self.logger.error(f"分析过程中发生错误: {str(e)}")
            raise
