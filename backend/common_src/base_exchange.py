import abc
from typing import Any, Dict, Optional, List

class BaseExchange(abc.ABC):
    """
    合约交易所抽象基类，所有交易所实现需继承本类
    """
    @abc.abstractmethod
    def ping_server(self) -> None:
        """测试服务器连通性"""
        pass

    @abc.abstractmethod
    def generate_signature(self, *args, **kwargs) -> str:
        """生成API请求签名"""
        pass

    @abc.abstractmethod
    def set_leverage(self, symbol: str, leverage: int) -> Any:
        """设置杠杆倍率"""
        pass

    @abc.abstractmethod
    def set_margin_type(self, symbol: str, margin_type: str) -> Any:
        """设置仓位模式"""
        pass

    @abc.abstractmethod
    def get_order_book(self, symbol: str, limit: int = 5) -> Dict[str, Any]:
        """获取订单簿深度"""
        pass

    @abc.abstractmethod
    def place_order(self, *args, **kwargs) -> Any:
        """下单接口（参数由子类定义）"""
        pass

    @abc.abstractmethod
    def cancel_order(self, symbol: str, order_id: int) -> Any:
        """取消订单"""
        pass

    @abc.abstractmethod
    def get_order_info(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """查询订单详情"""
        pass

    @abc.abstractmethod
    def query_position_info(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """查询持仓信息"""
        pass

    @abc.abstractmethod
    def query_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """查询未成交订单"""
        pass

    @abc.abstractmethod
    def clear_order(self) -> None:
        """清理所有持仓和未成交订单"""
        pass
