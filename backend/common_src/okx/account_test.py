#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX账户配置测试
检查账户模式、权限和配置
"""

import sys
import os
import time
import json
import logging
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from common_src.okx.okx_exchange import OkxFuturesAPI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

def test_account_configuration():
    """测试账户配置"""
    
    # 从环境变量获取API配置
    API_KEY = os.getenv('OKX_API_KEY', '829a2193-0860-4a18-a2bf-0a8167e6408e')
    SECRET_KEY = os.getenv('OKX_SECRET_KEY', '29C1565A65E80B31DB38442895630AE8')
    PASSPHRASE = os.getenv('OKX_PASSPHRASE', '@Aaliu7751541')
    
    print("="*60)
    print("OKX账户配置测试")
    print("="*60)
    print(f"API Key: {API_KEY[:10]}...")
    print("="*60)
    
    try:
        # 1. 初始化API
        print("\n1. 初始化OKX API...")
        api = OkxFuturesAPI(API_KEY, SECRET_KEY, PASSPHRASE)
        print("✅ API初始化成功")
        
        # 2. 测试服务器连通性
        print("\n2. 测试服务器连通性...")
        api.ping_server()
        print("✅ 服务器连通性测试通过")
        
        # 3. 获取账户余额
        print("\n3. 获取账户余额...")
        try:
            # 这里需要添加获取账户余额的方法
            print("⚠️ 账户余额查询功能需要实现")
        except Exception as e:
            print(f"❌ 账户余额查询失败: {e}")
        
        # 4. 获取账户配置
        print("\n4. 获取账户配置...")
        try:
            # 这里需要添加获取账户配置的方法
            print("⚠️ 账户配置查询功能需要实现")
        except Exception as e:
            print(f"❌ 账户配置查询失败: {e}")
        
        # 5. 测试不同交易模式
        print("\n5. 测试不同交易模式...")
        
        # 测试全仓模式
        print("   测试全仓模式 (cross)...")
        try:
            test_order_cross = api.place_order(
                instId='BTC-USDT-SWAP',
                tdMode='cross',
                side='buy',
                ordType='limit',
                sz='1',
                px='100000'  # 远低于市价的价格
            )
            print(f"   全仓模式结果: {test_order_cross}")
        except Exception as e:
            print(f"   全仓模式异常: {e}")
        
        # 测试逐仓模式
        print("   测试逐仓模式 (isolated)...")
        try:
            test_order_isolated = api.place_order(
                instId='BTC-USDT-SWAP',
                tdMode='isolated',
                side='buy',
                ordType='limit',
                sz='1',
                px='100000'  # 远低于市价的价格
            )
            print(f"   逐仓模式结果: {test_order_isolated}")
        except Exception as e:
            print(f"   逐仓模式异常: {e}")
        
        # 6. 测试市价单
        print("\n6. 测试市价单...")
        try:
            market_order = api.place_order(
                instId='BTC-USDT-SWAP',
                tdMode='cross',
                side='buy',
                ordType='market',
                sz='1'
            )
            print(f"   市价单结果: {market_order}")
        except Exception as e:
            print(f"   市价单异常: {e}")
        
        # 7. 检查账户权限
        print("\n7. 检查账户权限...")
        try:
            # 查询持仓
            positions = api.query_position_info('BTC-USDT-SWAP')
            print(f"   持仓查询权限: ✅")
            
            # 查询未成交订单
            open_orders = api.query_open_orders('BTC-USDT-SWAP')
            print(f"   订单查询权限: ✅")
            
            # 设置杠杆
            leverage_result = api.set_leverage('BTC-USDT-SWAP', 3)
            if leverage_result.get('code') == '0':
                print(f"   杠杆设置权限: ✅")
            else:
                print(f"   杠杆设置权限: ❌ - {leverage_result.get('msg')}")
                
        except Exception as e:
            print(f"   权限检查异常: {e}")
        
        # 8. 测试模拟交易
        print("\n8. 测试模拟交易...")
        try:
            # 修改API为模拟交易模式
            api.base_url = "https://www.okx.com"
            api._get_headers = lambda method, request_path, body="": {
                'OK-ACCESS-KEY': API_KEY,
                'OK-ACCESS-SIGN': 'test_sign',
                'OK-ACCESS-TIMESTAMP': '2025-07-27T17:30:00.000Z',
                'OK-ACCESS-PASSPHRASE': PASSPHRASE,
                'Content-Type': 'application/json',
                'x-simulated-trading': '1'  # 启用模拟交易
            }
            
            print("   注意: 模拟交易功能需要特殊配置")
            
        except Exception as e:
            print(f"   模拟交易测试异常: {e}")
        
        print("\n" + "="*60)
        print("✅ OKX账户配置测试完成")
        print("="*60)
        
        # 输出建议
        print("\n📋 建议:")
        print("1. 检查账户是否已激活期货交易权限")
        print("2. 确认账户模式是否支持当前操作")
        print("3. 检查账户余额是否充足")
        print("4. 考虑使用模拟交易进行测试")
        print("5. 联系OKX客服确认账户状态")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)

def main():
    """主函数"""
    test_account_configuration()

if __name__ == "__main__":
    main() 