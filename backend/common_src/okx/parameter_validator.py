#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX API 参数验证工具
用于验证和格式化OKX API调用参数
"""

import re
from typing import Dict, Any, List, Optional
from decimal import Decimal, InvalidOperation

class OkxParameterValidator:
    """OKX API参数验证器"""
    
    # 支持的合约列表
    SUPPORTED_CONTRACTS = [
        "BTC-USDT-SWAP", "ETH-USDT-SWAP", "SOL-USDT-SWAP", "BNB-USDT-SWAP",
        "ADA-USDT-SWAP", "DOT-USDT-SWAP", "LINK-USDT-SWAP", "UNI-USDT-SWAP",
        "LTC-USDT-SWAP", "IP-USDT-SWAP", "XRP-USDT-SWAP", "MATIC-USDT-SWAP",
        "AVAX-USDT-SWAP", "ATOM-USDT-SWAP", "NEAR-USDT-SWAP", "FTM-USDT-SWAP"
    ]
    
    # 支持的交易模式
    SUPPORTED_TD_MODES = ["cross", "isolated"]
    
    # 支持的交易方向
    SUPPORTED_SIDES = ["buy", "sell"]
    
    # 支持的订单类型
    SUPPORTED_ORDER_TYPES = ["limit", "market", "post_only", "fok", "ioc"]
    
    def __init__(self):
        """初始化验证器"""
        pass
    
    def validate_order_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证下单参数
        
        Args:
            params: 下单参数字典
            
        Returns:
            Dict: 验证后的参数字典
            
        Raises:
            ValueError: 参数验证失败
        """
        # 检查必需参数
        required_fields = ["instId", "tdMode", "side", "ordType", "sz"]
        for field in required_fields:
            if field not in params:
                raise ValueError(f"缺少必需参数: {field}")
        
        # 验证合约ID
        inst_id = params["instId"]
        if not self._is_valid_contract(inst_id):
            raise ValueError(f"无效的合约ID: {inst_id}")
        
        # 验证交易模式
        td_mode = params["tdMode"]
        if td_mode not in self.SUPPORTED_TD_MODES:
            raise ValueError(f"无效的交易模式: {td_mode}")
        
        # 验证交易方向
        side = params["side"]
        if side not in self.SUPPORTED_SIDES:
            raise ValueError(f"无效的交易方向: {side}")
        
        # 验证订单类型
        ord_type = params["ordType"]
        if ord_type not in self.SUPPORTED_ORDER_TYPES:
            raise ValueError(f"无效的订单类型: {ord_type}")
        
        # 验证数量
        sz = params["sz"]
        if not self._is_valid_number(sz):
            raise ValueError(f"无效的数量: {sz}")
        
        # 验证价格（限价单必需）
        if ord_type == "limit":
            if "px" not in params:
                raise ValueError("限价单必须指定价格")
            px = params["px"]
            if not self._is_valid_number(px):
                raise ValueError(f"无效的价格: {px}")
            if float(px) <= 0:
                raise ValueError("价格必须大于0")
        
        # 验证自定义订单ID（可选）
        if "clOrdId" in params:
            cl_ord_id = params["clOrdId"]
            if not self._is_valid_client_order_id(cl_ord_id):
                raise ValueError(f"无效的自定义订单ID: {cl_ord_id}")
        
        return params
    
    def validate_leverage_params(self, symbol: str, leverage: int) -> Dict[str, Any]:
        """
        验证杠杆设置参数
        
        Args:
            symbol: 合约ID
            leverage: 杠杆倍数
            
        Returns:
            Dict: 验证后的参数
            
        Raises:
            ValueError: 参数验证失败
        """
        if not self._is_valid_contract(symbol):
            raise ValueError(f"无效的合约ID: {symbol}")
        
        if not isinstance(leverage, int) or leverage < 1 or leverage > 125:
            raise ValueError(f"无效的杠杆倍数: {leverage}，范围应为1-125")
        
        return {
            "instId": symbol,
            "lever": str(leverage),
            "mgnMode": "cross"
        }
    
    def validate_query_params(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        验证查询参数
        
        Args:
            symbol: 合约ID（可选）
            
        Returns:
            Dict: 验证后的参数
            
        Raises:
            ValueError: 参数验证失败
        """
        params = {"instType": "SWAP"}
        
        if symbol:
            if not self._is_valid_contract(symbol):
                raise ValueError(f"无效的合约ID: {symbol}")
            params["instId"] = symbol
        
        return params
    
    def format_order_params(self, 
                          symbol: str, 
                          side: str, 
                          order_type: str, 
                          size: str, 
                          price: Optional[str] = None,
                          td_mode: str = "cross",
                          client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """
        格式化下单参数
        
        Args:
            symbol: 合约ID
            side: 交易方向
            order_type: 订单类型
            size: 数量
            price: 价格（限价单必需）
            td_mode: 交易模式
            client_order_id: 自定义订单ID
            
        Returns:
            Dict: 格式化的参数字典
        """
        params = {
            "instId": symbol,
            "tdMode": td_mode,
            "side": side,
            "ordType": order_type,
            "sz": size
        }
        
        if price:
            params["px"] = price
        
        if client_order_id:
            params["clOrdId"] = client_order_id
        
        return self.validate_order_params(params)
    
    def create_limit_order_params(self, 
                                symbol: str, 
                                side: str, 
                                size: str, 
                                price: str,
                                td_mode: str = "cross") -> Dict[str, Any]:
        """
        创建限价单参数
        
        Args:
            symbol: 合约ID
            side: 交易方向
            size: 数量
            price: 价格
            td_mode: 交易模式
            
        Returns:
            Dict: 限价单参数字典
        """
        return self.format_order_params(
            symbol=symbol,
            side=side,
            order_type="limit",
            size=size,
            price=price,
            td_mode=td_mode
        )
    
    def create_market_order_params(self, 
                                 symbol: str, 
                                 side: str, 
                                 size: str,
                                 td_mode: str = "cross") -> Dict[str, Any]:
        """
        创建市价单参数
        
        Args:
            symbol: 合约ID
            side: 交易方向
            size: 数量
            td_mode: 交易模式
            
        Returns:
            Dict: 市价单参数字典
        """
        return self.format_order_params(
            symbol=symbol,
            side=side,
            order_type="market",
            size=size,
            td_mode=td_mode
        )
    
    def _is_valid_contract(self, contract: str) -> bool:
        """验证合约ID格式"""
        if not isinstance(contract, str):
            return False
        
        # 检查是否在支持的合约列表中
        if contract in self.SUPPORTED_CONTRACTS:
            return True
        
        # 检查合约格式：币种-USDT-SWAP
        pattern = r'^[A-Z]{2,10}-USDT-SWAP$'
        return bool(re.match(pattern, contract))
    
    def _is_valid_number(self, value: Any) -> bool:
        """验证数字格式"""
        try:
            float_val = float(value)
            return float_val > 0
        except (ValueError, TypeError):
            return False
    
    def _is_valid_client_order_id(self, client_order_id: str) -> bool:
        """验证自定义订单ID格式"""
        if not isinstance(client_order_id, str):
            return False
        
        # OKX要求：1-32位字符，字母数字组合
        if len(client_order_id) < 1 or len(client_order_id) > 32:
            return False
        
        # 只允许字母和数字
        pattern = r'^[a-zA-Z0-9]+$'
        return bool(re.match(pattern, client_order_id))
    
    def get_supported_contracts(self) -> List[str]:
        """获取支持的合约列表"""
        return self.SUPPORTED_CONTRACTS.copy()
    
    def get_contract_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取合约信息
        
        Args:
            symbol: 合约ID
            
        Returns:
            Dict: 合约信息
        """
        if not self._is_valid_contract(symbol):
            raise ValueError(f"无效的合约ID: {symbol}")
        
        # 这里可以添加更多合约信息
        return {
            "symbol": symbol,
            "type": "SWAP",
            "quote_currency": "USDT",
            "base_currency": symbol.split('-')[0]
        }

# 使用示例
def example_usage():
    """使用示例"""
    validator = OkxParameterValidator()
    
    try:
        # 验证限价单参数
        limit_params = validator.create_limit_order_params(
            symbol="BTC-USDT-SWAP",
            side="buy",
            size="0.01",
            price="1000"
        )
        print("限价单参数验证通过:", limit_params)
        
        # 验证市价单参数
        market_params = validator.create_market_order_params(
            symbol="BTC-USDT-SWAP",
            side="sell",
            size="1"
        )
        print("市价单参数验证通过:", market_params)
        
        # 验证杠杆参数
        leverage_params = validator.validate_leverage_params("BTC-USDT-SWAP", 3)
        print("杠杆参数验证通过:", leverage_params)
        
    except ValueError as e:
        print(f"参数验证失败: {e}")

if __name__ == "__main__":
    example_usage() 