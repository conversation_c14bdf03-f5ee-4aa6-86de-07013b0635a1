#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX合约下单验证测试
基于OKX官方API文档实现完整的合约下单功能验证
"""

import sys
import os
import time
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from common_src.okx.okx_exchange import OkxFuturesAPI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

class OkxFuturesTradingTest:
    """OKX期货合约交易测试类"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: str, is_simulated: bool = True):
        """
        初始化测试类
        
        Args:
            api_key: OKX API Key
            secret_key: OKX Secret Key  
            passphrase: OKX API Passphrase
            is_simulated: 是否启用模拟交易模式，默认为True
        """
        self.api = OkxFuturesAPI(api_key, secret_key, passphrase, is_simulated=is_simulated)
        self.test_symbol = 'BTC-USDT-SWAP'  # 测试合约
        logger.info(f"初始化OKX期货交易测试，测试合约: {self.test_symbol}, 模拟交易: {is_simulated}")
    
    def test_server_connectivity(self) -> Dict[str, Any]:
        """
        测试服务器连通性
        
        Returns:
            Dict: 连接结果
        """
        try:
            logger.info("测试OKX服务器连通性...")
            self.api.ping_server()
            logger.info("✅ 服务器连通性测试通过")
            return {"success": True}
        except Exception as e:
            logger.error(f"❌ 服务器连通性测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_account_permissions(self) -> Dict[str, Any]:
        """
        测试账户权限和配置
        
        Returns:
            Dict: 账户信息
        """
        try:
            logger.info("测试账户权限...")
            
            # 查询持仓信息
            positions = self.api.query_position_info(self.test_symbol)
            logger.info(f"当前持仓: {positions}")
            
            # 查询未成交订单
            open_orders = self.api.query_open_orders(self.test_symbol)
            logger.info(f"未成交订单: {open_orders}")
            
            return {
                "success": True,
                "positions": positions,
                "open_orders": open_orders
            }
        except Exception as e:
            logger.error(f"❌ 账户权限测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_market_data(self) -> Dict[str, Any]:
        """
        测试市场数据获取
        
        Returns:
            Dict: 市场数据
        """
        try:
            logger.info("获取市场数据...")
            
            # 获取订单簿
            order_book = self.api.get_order_book(self.test_symbol, limit=5)
            logger.info(f"订单簿数据: {order_book}")
            
            if order_book.get('code') == '0' and order_book.get('data'):
                best_bid = order_book['data'][0]['bids'][0][0] if order_book['data'][0]['bids'] else None
                best_ask = order_book['data'][0]['asks'][0][0] if order_book['data'][0]['asks'] else None
                
                return {
                    "success": True,
                    "best_bid": best_bid,
                    "best_ask": best_ask,
                    "spread": float(best_ask) - float(best_bid) if best_bid and best_ask else None
                }
            else:
                return {"success": False, "error": "无法获取订单簿数据"}
                
        except Exception as e:
            logger.error(f"❌ 市场数据测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_leverage_setting(self) -> Dict[str, Any]:
        """
        测试杠杆设置
        
        Returns:
            Dict: 杠杆设置结果
        """
        try:
            logger.info("测试杠杆设置...")
            
            # 设置杠杆为3倍
            result = self.api.set_leverage(self.test_symbol, 3)
            logger.info(f"杠杆设置结果: {result}")
            
            return {
                "success": result.get('code') == '0',
                "result": result
            }
        except Exception as e:
            logger.error(f"❌ 杠杆设置测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_limit_order_placement(self, side: str = 'buy', size: str = '1', 
                                 price_offset: float = -0.1) -> Dict[str, Any]:
        """
        测试限价单下单
        
        Args:
            side: 交易方向 'buy' 或 'sell'
            size: 交易数量
            price_offset: 价格偏移百分比（相对于最优价格）
        
        Returns:
            Dict: 下单结果
        """
        try:
            logger.info(f"测试{side}限价单下单...")
            
            # 获取市场数据
            market_data = self.test_market_data()
            if not market_data['success']:
                return {"success": False, "error": "无法获取市场数据"}
            
            # 计算下单价格
            if side == 'buy':
                base_price = float(market_data['best_bid'])
            else:
                base_price = float(market_data['best_ask'])
            
            order_price = base_price * (1 + price_offset)
            
            # 下单 - 使用正确的参数格式
            order_params = {
                "instId": self.test_symbol,
                "tdMode": "cross",
                "side": side,
                "ordType": "limit",
                "px": f"{order_price:.2f}",
                "sz": size
            }
            
            logger.info(f"下单参数: {order_params}")
            order_result = self.api.place_order(**order_params)
            
            logger.info(f"限价单下单结果: {order_result}")
            
            if order_result.get('code') == '0':
                order_id = order_result.get('data', [{}])[0].get('ordId')
                
                # 查询订单详情
                order_info = self.api.get_order_info(self.test_symbol, order_id)
                logger.info(f"订单详情: {order_info}")
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "order_price": order_price,
                    "order_result": order_result,
                    "order_info": order_info
                }
            else:
                return {
                    "success": False,
                    "error": order_result.get('msg', '下单失败'),
                    "order_result": order_result
                }
                
        except Exception as e:
            logger.error(f"❌ 限价单下单测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_market_order_placement(self, side: str = 'buy', size: str = '1') -> Dict[str, Any]:
        """
        测试市价单下单
        
        Args:
            side: 交易方向 'buy' 或 'sell'
            size: 交易数量
        
        Returns:
            Dict: 下单结果
        """
        try:
            logger.info(f"测试{side}市价单下单...")
            
            # 下单 - 使用正确的参数格式
            order_params = {
                "instId": self.test_symbol,
                "tdMode": "cross",
                "side": side,
                "ordType": "market",
                "sz": size
            }
            
            logger.info(f"下单参数: {order_params}")
            order_result = self.api.place_order(**order_params)
            
            logger.info(f"市价单下单结果: {order_result}")
            
            if order_result.get('code') == '0':
                order_id = order_result.get('data', [{}])[0].get('ordId')
                
                # 查询订单详情
                order_info = self.api.get_order_info(self.test_symbol, order_id)
                logger.info(f"订单详情: {order_info}")
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "order_result": order_result,
                    "order_info": order_info
                }
            else:
                return {
                    "success": False,
                    "error": order_result.get('msg', '下单失败'),
                    "order_result": order_result
                }
                
        except Exception as e:
            logger.error(f"❌ 市价单下单测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_order_cancellation(self, order_id: str) -> Dict[str, Any]:
        """
        测试订单撤销
        
        Args:
            order_id: 订单ID
        
        Returns:
            Dict: 撤销结果
        """
        try:
            logger.info(f"测试撤销订单: {order_id}")
            
            cancel_result = self.api.cancel_order(self.test_symbol, order_id)
            logger.info(f"撤销订单结果: {cancel_result}")
            
            return {
                "success": cancel_result.get('code') == '0',
                "cancel_result": cancel_result
            }
        except Exception as e:
            logger.error(f"❌ 订单撤销测试失败: {e}")
            return {"success": False, "error": str(e)}
    

    
    def test_correct_order_parameters(self) -> Dict[str, Any]:
        """
        测试正确的下单参数格式
        
        Returns:
            Dict: 测试结果
        """
        try:
            logger.info("测试正确的下单参数格式...")
            
            # 使用您提供的正确参数格式
            order_params = {
                "instId": "BTC-USDT-SWAP",
                "tdMode": "cross",
                "side": "buy",
                "ordType": "limit",
                "px": "1000",
                "sz": "0.01"
            }
            
            logger.info(f"测试下单参数: {order_params}")
            order_result = self.api.place_order(**order_params)
            
            logger.info(f"下单结果: {order_result}")
            
            if order_result.get('code') == '0':
                order_id = order_result.get('data', [{}])[0].get('ordId')
                
                # 查询订单详情
                order_info = self.api.get_order_info(self.test_symbol, order_id)
                logger.info(f"订单详情: {order_info}")
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "order_params": order_params,
                    "order_result": order_result,
                    "order_info": order_info
                }
            else:
                return {
                    "success": False,
                    "error": order_result.get('msg', '下单失败'),
                    "order_result": order_result
                }
                
        except Exception as e:
            logger.error(f"❌ 正确参数格式测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_position_management(self) -> Dict[str, Any]:
        """
        测试持仓管理
        
        Returns:
            Dict: 持仓管理结果
        """
        try:
            logger.info("测试持仓管理...")
            
            # 查询当前持仓
            positions = self.api.query_position_info(self.test_symbol)
            logger.info(f"当前持仓: {positions}")
            
            # 如果有持仓，尝试平仓
            if positions and any(float(pos.get('pos', '0')) != 0 for pos in positions):
                logger.info("检测到持仓，尝试平仓...")
                
                for position in positions:
                    pos_size = float(position.get('pos', '0'))
                    if pos_size != 0:
                        # 确定平仓方向
                        close_side = 'sell' if pos_size > 0 else 'buy'
                        
                        # 市价平仓 - 使用正确的参数格式
                        close_params = {
                            "instId": self.test_symbol,
                            "tdMode": "cross",
                            "side": close_side,
                            "ordType": "market",
                            "sz": str(abs(pos_size))
                        }
                        
                        logger.info(f"平仓参数: {close_params}")
                        close_result = self.api.place_order(**close_params)
                        
                        logger.info(f"平仓结果: {close_result}")
            
            return {
                "success": True,
                "positions": positions
            }
        except Exception as e:
            logger.error(f"❌ 持仓管理测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        运行综合测试
        
        Returns:
            Dict: 综合测试结果
        """
        logger.info("开始运行OKX期货合约综合测试...")
        
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {}
        }
        
        # 1. 服务器连通性测试
        test_results["tests"]["server_connectivity"] = self.test_server_connectivity()
        
        # 2. 账户权限测试
        test_results["tests"]["account_permissions"] = self.test_account_permissions()
        
        # 3. 市场数据测试
        test_results["tests"]["market_data"] = self.test_market_data()
        
        # 4. 杠杆设置测试
        test_results["tests"]["leverage_setting"] = self.test_leverage_setting()
        
        # 5. 正确参数格式测试
        test_results["tests"]["correct_parameters"] = self.test_correct_order_parameters()
        
        # 6. 限价单测试
        test_results["tests"]["limit_order_buy"] = self.test_limit_order_placement('buy', '1', -0.1)
        test_results["tests"]["limit_order_sell"] = self.test_limit_order_placement('sell', '1', 0.1)
        
        # 7. 市价单测试（谨慎使用）
        # test_results["tests"]["market_order"] = self.test_market_order_placement('buy', '1')
        
        # 8. 持仓管理测试
        test_results["tests"]["position_management"] = self.test_position_management()
        
        # 统计测试结果
        success_count = sum(1 for test in test_results["tests"].values() 
                          if isinstance(test, dict) and test.get('success', False))
        total_count = len(test_results["tests"])
        
        test_results["summary"] = {
            "total_tests": total_count,
            "successful_tests": success_count,
            "success_rate": f"{success_count/total_count*100:.1f}%"
        }
        
        logger.info(f"综合测试完成，成功率: {test_results['summary']['success_rate']}")
        
        return test_results

def main():
    """主函数"""
    # 从环境变量获取API配置
    API_KEY = os.getenv('OKX_API_KEY', '2ea4d261-c95c-4871-acdf-8575d79205af')
    SECRET_KEY = os.getenv('OKX_SECRET_KEY', 'BE72D32C58B6E2CC3D1C7B090DD0A01C')
    PASSPHRASE = os.getenv('OKX_PASSPHRASE', '@Aaliu7751541')
    
    # 从环境变量获取是否启用模拟交易模式
    IS_SIMULATED = os.getenv('OKX_SIMULATED', 'true').lower() == 'true'
    
    if API_KEY == 'your_api_key_here':
        logger.error("请设置OKX API配置环境变量")
        logger.info("设置方法:")
        logger.info("export OKX_API_KEY='your_api_key'")
        logger.info("export OKX_SECRET_KEY='your_secret_key'")
        logger.info("export OKX_PASSPHRASE='your_passphrase'")
        logger.info("export OKX_SIMULATED='true'  # 设置为false禁用模拟交易")
        return
    
    logger.info(f"使用模拟交易模式: {IS_SIMULATED}")
    
    # 创建测试实例
    tester = OkxFuturesTradingTest(API_KEY, SECRET_KEY, PASSPHRASE, is_simulated=IS_SIMULATED)
    
    # 运行综合测试
    results = tester.run_comprehensive_test()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("OKX期货合约下单验证测试结果")
    print("="*50)
    
    for test_name, test_result in results["tests"].items():
        status = "✅ 通过" if isinstance(test_result, dict) and test_result.get('success', False) else "❌ 失败"
        print(f"{test_name}: {status}")
        
        if isinstance(test_result, dict) and not test_result.get('success', False):
            print(f"  错误: {test_result.get('error', '未知错误')}")
    
    print(f"\n测试总结:")
    print(f"总测试数: {results['summary']['total_tests']}")
    print(f"成功测试: {results['summary']['successful_tests']}")
    print(f"成功率: {results['summary']['success_rate']}")
    
    # 保存详细结果到文件
    with open('okx_futures_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细测试结果已保存到: okx_futures_test_results.json")

if __name__ == "__main__":
    main()
