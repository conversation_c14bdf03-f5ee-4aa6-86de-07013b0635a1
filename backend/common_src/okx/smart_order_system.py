#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能下单系统
实现下单并循环获取订单状态，如果订单10秒未成交，直接撤单并重新提交订单
保证下单数量完整提交并成交
"""

import sys
import os
import time
import logging
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# 添加项目路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from common_src.okx.okx_exchange import OkxFuturesAPI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "pending"      # 待成交
    PARTIAL = "partial"      # 部分成交
    FILLED = "filled"        # 完全成交
    CANCELLED = "cancelled"  # 已撤销
    FAILED = "failed"        # 失败

@dataclass
class OrderResult:
    """订单结果数据类"""
    order_id: str
    status: OrderStatus
    filled_size: float = 0.0
    remaining_size: float = 0.0
    avg_price: float = 0.0
    total_cost: float = 0.0
    message: str = ""
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class SmartOrderSystem:
    """智能下单系统"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: str, is_simulated: bool = True):
        """
        初始化智能下单系统
        
        Args:
            api_key: OKX API Key
            secret_key: OKX Secret Key
            passphrase: OKX API Passphrase
            is_simulated: 是否启用模拟交易模式
        """
        self.api = OkxFuturesAPI(api_key, secret_key, passphrase, is_simulated=is_simulated)
        self.max_retry_times = 5  # 最大重试次数
        self.order_timeout = 10   # 订单超时时间（秒）
        self.check_interval = 1   # 检查间隔（秒）
        self.price_adjustment = 0.001  # 价格调整幅度（0.1%）
        logger.info(f"智能下单系统初始化完成，模拟交易: {is_simulated}")
    
    def get_order_status(self, symbol: str, order_id: str) -> OrderResult:
        """
        获取订单状态
        
        Args:
            symbol: 交易对
            order_id: 订单ID
        
        Returns:
            OrderResult: 订单状态结果
        """
        try:
            order_info = self.api.get_order_info(symbol, order_id)
            
            if order_info.get('code') != '0':
                return OrderResult(
                    order_id=order_id,
                    status=OrderStatus.FAILED,
                    message=f"获取订单信息失败: {order_info.get('msg', '未知错误')}"
                )
            
            order_data = order_info.get('data', [{}])[0]
            state = order_data.get('state', '')
            
            # 安全地解析数值字段，处理空字符串的情况
            try:
                filled_size = float(order_data.get('fillSz', '0') or '0')
            except (ValueError, TypeError):
                filled_size = 0.0
                
            try:
                total_size = float(order_data.get('sz', '0') or '0')
            except (ValueError, TypeError):
                total_size = 0.0
                
            try:
                avg_price = float(order_data.get('avgPx', '0') or '0')
            except (ValueError, TypeError):
                avg_price = 0.0
                
            remaining_size = total_size - filled_size
            
            # 计算总成本
            total_cost = filled_size * avg_price if avg_price > 0 else 0
            
            # 判断订单状态
            if state == 'live':
                status = OrderStatus.PENDING
            elif state == 'filled':
                status = OrderStatus.FILLED
            elif state == 'canceled':
                status = OrderStatus.CANCELLED
            elif filled_size > 0 and filled_size < total_size:
                status = OrderStatus.PARTIAL
            else:
                status = OrderStatus.FAILED
            
            return OrderResult(
                order_id=order_id,
                status=status,
                filled_size=filled_size,
                remaining_size=remaining_size,
                avg_price=avg_price,
                total_cost=total_cost,
                message=f"订单状态: {state}"
            )
            
        except Exception as e:
            logger.error(f"获取订单状态失败: {e}")
            return OrderResult(
                order_id=order_id,
                status=OrderStatus.FAILED,
                message=f"获取订单状态异常: {str(e)}"
            )
    
    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """
        撤销订单
        
        Args:
            symbol: 交易对
            order_id: 订单ID
        
        Returns:
            bool: 撤销是否成功
        """
        try:
            result = self.api.cancel_order(symbol, order_id)
            success = result.get('code') == '0'
            
            if success:
                logger.info(f"✅ 订单撤销成功: {order_id}")
            else:
                logger.error(f"❌ 订单撤销失败: {order_id}, 错误: {result.get('msg', '未知错误')}")
            
            return success
            
        except Exception as e:
            logger.error(f"撤销订单异常: {e}")
            return False
    
    def calculate_optimal_price(self, symbol: str, side: str, price_offset_percent: float = 0.1) -> Dict[str, Any]:
        """
        计算最优价格
        
        Args:
            symbol: 交易对
            side: 交易方向
            price_offset_percent: 价格偏移百分比
        
        Returns:
            Dict: 包含最优价格的字典
        """
        try:
            # 获取订单簿
            order_book = self.api.get_order_book(symbol, limit=5)
            if not order_book.get('data') or not order_book['data']:
                return {"success": False, "error": "无法获取订单簿数据"}
            
            best_bid = float(order_book['data'][0]['bids'][0][0]) if order_book['data'][0]['bids'] else None
            best_ask = float(order_book['data'][0]['asks'][0][0]) if order_book['data'][0]['asks'] else None
            
            if not best_bid or not best_ask:
                return {"success": False, "error": "无法获取有效的买卖价格"}
            
            # 计算最优价格
            if side == 'buy':
                optimal_price = best_bid * (1 + price_offset_percent / 100)  # 买单价格略高于买一价
            else:
                optimal_price = best_ask * (1 - price_offset_percent / 100)  # 卖单价格略低于卖一价
            
            return {
                "success": True,
                "optimal_price": str(optimal_price),
                "market_data": {
                    "best_bid": best_bid,
                    "best_ask": best_ask,
                    "spread": best_ask - best_bid
                }
            }
            
        except Exception as e:
            logger.error(f"计算最优价格失败: {e}")
            return {"success": False, "error": str(e)}
    
    def place_order_with_retry(self, symbol: str, side: str, size: str, 
                              order_type: str = "limit", price: Optional[str] = None,
                              max_retries: int = 3) -> Dict[str, Any]:
        """
        带重试的下单
        
        Args:
            symbol: 交易对
            side: 交易方向
            size: 数量
            order_type: 订单类型
            price: 价格（限价单必需）
            max_retries: 最大重试次数
        
        Returns:
            Dict: 下单结果
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"下单尝试 {attempt + 1}/{max_retries}")
                
                # 构建订单参数
                order_params = {
                    "instId": symbol,
                    "tdMode": "cross",
                    "side": side,
                    "ordType": order_type,
                    "sz": size
                }
                
                if price:
                    order_params["px"] = price
                
                # 下单
                result = self.api.place_order(**order_params)
                
                if result.get('code') == '0':
                    order_id = result.get('data', [{}])[0].get('ordId', '')
                    logger.info(f"✅ 下单成功: {order_id}")
                    return {
                        "success": True,
                        "order_id": order_id,
                        "attempt": attempt + 1,
                        "result": result
                    }
                else:
                    logger.warning(f"下单失败 (尝试 {attempt + 1}): {result.get('msg', '未知错误')}")
                    
                    # 如果是最后一次尝试，返回失败结果
                    if attempt == max_retries - 1:
                        return {
                            "success": False,
                            "error": result.get('msg', '下单失败'),
                            "result": result
                        }
                    
                    # 等待一段时间后重试
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"下单异常 (尝试 {attempt + 1}): {e}")
                
                if attempt == max_retries - 1:
                    return {
                        "success": False,
                        "error": str(e)
                    }
                
                time.sleep(1)
        
        return {"success": False, "error": "达到最大重试次数"}
    
    def monitor_order_with_timeout(self, symbol: str, order_id: str, 
                                 timeout_seconds: int = 10) -> OrderResult:
        """
        监控订单状态，超时自动撤销
        
        Args:
            symbol: 交易对
            order_id: 订单ID
            timeout_seconds: 超时时间（秒）
        
        Returns:
            OrderResult: 订单结果
        """
        start_time = time.time()
        
        while True:
            # 检查是否超时
            elapsed_time = time.time() - start_time
            if elapsed_time >= timeout_seconds:
                logger.warning(f"订单 {order_id} 超时 ({timeout_seconds}秒)，准备撤销")
                
                # 撤销订单
                if self.cancel_order(symbol, order_id):
                    return OrderResult(
                        order_id=order_id,
                        status=OrderStatus.CANCELLED,
                        message=f"订单超时撤销 ({timeout_seconds}秒)"
                    )
                else:
                    return OrderResult(
                        order_id=order_id,
                        status=OrderStatus.FAILED,
                        message="订单超时且撤销失败"
                    )
            
            # 获取订单状态
            order_result = self.get_order_status(symbol, order_id)
            
            # 如果订单已完成（成交或撤销），返回结果
            if order_result.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.FAILED]:
                logger.info(f"订单 {order_id} 状态: {order_result.status.value}")
                return order_result
            
            # 如果订单部分成交，记录日志
            if order_result.status == OrderStatus.PARTIAL:
                logger.info(f"订单 {order_id} 部分成交: {order_result.filled_size}/{order_result.filled_size + order_result.remaining_size}")
            
            # 等待一段时间后继续检查
            time.sleep(self.check_interval)
    
    def smart_order_with_retry(self, symbol: str, side: str, total_size: str,
                              order_type: str = "limit", price: Optional[str] = None,
                              price_offset_percent: float = 0.1) -> Dict[str, Any]:
        """
        智能下单（带重试和价格调整）
        
        Args:
            symbol: 交易对
            side: 交易方向
            total_size: 总数量
            order_type: 订单类型
            price: 初始价格
            price_offset_percent: 价格偏移百分比
        
        Returns:
            Dict: 下单结果
        """
        remaining_size = float(total_size)
        total_filled = 0.0
        total_cost = 0.0
        orders_placed = []
        retry_count = 0
        
        logger.info(f"开始智能下单: {symbol} {side} {total_size}")
        
        while remaining_size > 0 and retry_count < self.max_retry_times:
            retry_count += 1
            logger.info(f"第 {retry_count} 次尝试，剩余数量: {remaining_size}")
            
            # 每次下单前都获取最新价格
            price_result = self.calculate_optimal_price(symbol, side, price_offset_percent)
            if not price_result['success']:
                return {
                    "success": False,
                    "error": f"获取最新价格失败: {price_result['error']}",
                    "partial_result": {
                        "total_filled": total_filled,
                        "total_cost": total_cost,
                        "orders_placed": orders_placed
                    }
                }
            
            # 使用最新价格，并根据重试次数进行微调
            base_price = float(price_result['optimal_price'])
            if retry_count > 1:
                # 重试时稍微调整价格以确保成交
                if side == 'buy':
                    current_price = str(base_price * (1 + (retry_count - 1) * self.price_adjustment))
                else:
                    current_price = str(base_price * (1 - (retry_count - 1) * self.price_adjustment))
            else:
                current_price = price_result['optimal_price']
            
            logger.info(f"获取最新价格: {price_result['optimal_price']}")
            logger.info(f"市场数据: 买一价={price_result['market_data']['best_bid']}, 卖一价={price_result['market_data']['best_ask']}")
            logger.info(f"调整后价格: {current_price} (重试次数: {retry_count})")
            
            # 下单
            order_result = self.place_order_with_retry(
                symbol=symbol,
                side=side,
                size=str(remaining_size),
                order_type=order_type,
                price=current_price
            )
            
            if not order_result['success']:
                logger.error(f"下单失败: {order_result['error']}")
                continue
            
            order_id = order_result['order_id']
            orders_placed.append({
                "order_id": order_id,
                "price": current_price,
                "size": remaining_size,
                "attempt": retry_count
            })
            
            # 监控订单状态
            monitor_result = self.monitor_order_with_timeout(symbol, order_id, self.order_timeout)
            
            if monitor_result.status == OrderStatus.FILLED:
                # 订单完全成交
                filled_size = monitor_result.filled_size
                total_filled += filled_size
                total_cost += monitor_result.total_cost
                remaining_size -= filled_size
                
                logger.info(f"✅ 订单完全成交: {order_id}, 成交数量: {filled_size}, 成交均价: {monitor_result.avg_price}")
                
            elif monitor_result.status == OrderStatus.PARTIAL:
                # 订单部分成交
                filled_size = monitor_result.filled_size
                total_filled += filled_size
                total_cost += monitor_result.total_cost
                remaining_size -= filled_size
                
                logger.info(f"⚠️ 订单部分成交: {order_id}, 成交数量: {filled_size}, 剩余数量: {remaining_size}")
                
            elif monitor_result.status == OrderStatus.CANCELLED:
                # 订单被撤销，继续重试
                logger.info(f"🔄 订单被撤销: {order_id}, 继续重试")
                
            else:
                # 订单失败
                logger.error(f"❌ 订单失败: {order_id}, 状态: {monitor_result.status.value}")
        
        # 计算最终结果
        avg_price = total_cost / total_filled if total_filled > 0 else 0
        
        if remaining_size <= 0:
            # 完全成交
            return {
                "success": True,
                "total_filled": total_filled,
                "total_cost": total_cost,
                "avg_price": avg_price,
                "orders_placed": orders_placed,
                "message": "所有订单完全成交"
            }
        else:
            # 部分成交
            return {
                "success": False,
                "total_filled": total_filled,
                "total_cost": total_cost,
                "avg_price": avg_price,
                "remaining_size": remaining_size,
                "orders_placed": orders_placed,
                "message": f"部分成交，剩余数量: {remaining_size}"
            }
    
    def smart_post_only_order(self, symbol: str, side: str, size: str,
                            price: Optional[str] = None) -> Dict[str, Any]:
        """
        智能Post-Only订单（确保只挂单不吃单）
        
        Args:
            symbol: 交易对
            side: 交易方向
            size: 数量
            price: 价格
        
        Returns:
            Dict: 下单结果
        """
        try:
            # 如果没有提供价格，计算安全价格
            if price is None:
                price_result = self.api.calculate_safe_limit_price(symbol, side, 0.1)
                if not price_result['success']:
                    return {"success": False, "error": f"计算安全价格失败: {price_result['error']}"}
                price = price_result['safe_price']
            
            # 下Post-Only订单
            order_result = self.api.place_post_only_order_safe(
                symbol=symbol,
                side=side,
                size=size,
                price=price
            )
            
            if order_result['success']:
                order_id = order_result['order_id']
                
                # 监控订单状态
                monitor_result = self.monitor_order_with_timeout(symbol, order_id, self.order_timeout)
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "price": price,
                    "size": size,
                    "monitor_result": monitor_result,
                    "order_result": order_result
                }
            else:
                return {
                    "success": False,
                    "error": order_result.get('error', 'Post-Only下单失败'),
                    "order_result": order_result
                }
                
        except Exception as e:
            logger.error(f"智能Post-Only订单失败: {e}")
            return {"success": False, "error": str(e)}

def main():
    """主函数 - 测试智能下单系统"""
    # 从环境变量获取API配置
    API_KEY = os.getenv('OKX_API_KEY', '2ea4d261-c95c-4871-acdf-8575d79205af')
    SECRET_KEY = os.getenv('OKX_SECRET_KEY', 'BE72D32C58B6E2CC3D1C7B090DD0A01C')
    PASSPHRASE = os.getenv('OKX_PASSPHRASE', '@Aaliu7751541')
    IS_SIMULATED = os.getenv('OKX_SIMULATED', 'true').lower() == 'true'
    
    if API_KEY == 'your_api_key_here':
        logger.error("请设置OKX API配置环境变量")
        return
    
    # 创建智能下单系统
    smart_system = SmartOrderSystem(API_KEY, SECRET_KEY, PASSPHRASE, is_simulated=IS_SIMULATED)
    
    # 测试参数
    test_symbol = "ETH-USDT-SWAP"
    test_side = "buy"
    test_size = "100"
    
    logger.info(f"开始测试智能下单系统...")
    logger.info(f"测试参数: {test_symbol} {test_side} {test_size}")
    
    # 测试智能下单
    result = smart_system.smart_order_with_retry(
        symbol=test_symbol,
        side=test_side,
        total_size=test_size,
        order_type="limit"
    )
    
    # 输出结果
    print("\n" + "="*60)
    print("智能下单系统测试结果")
    print("="*60)
    
    if result['success']:
        print(f"✅ 下单成功")
        print(f"   成交数量: {result['total_filled']}")
        print(f"   成交均价: {result['avg_price']}")
        print(f"   总成本: {result['total_cost']}")
        print(f"   订单数量: {len(result['orders_placed'])}")
    else:
        print(f"❌ 下单失败: {result.get('error', '未知错误')}")
        if 'total_filled' in result:
            print(f"   部分成交数量: {result['total_filled']}")
            print(f"   剩余数量: {result.get('remaining_size', 0)}")
    
    print("\n订单详情:")
    for i, order in enumerate(result.get('orders_placed', []), 1):
        print(f"  订单 {i}: ID={order['order_id']}, 价格={order['price']}, 数量={order['size']}")

if __name__ == "__main__":
    main() 