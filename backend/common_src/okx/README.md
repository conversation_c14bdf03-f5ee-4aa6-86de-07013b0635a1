# OKX API 文档总览

## 📚 文档列表

### 1. [详细参数文档](OKX_API_PARAMETERS.md)
- 完整的API参数说明
- 详细的代码示例
- 错误处理和最佳实践
- 测试用例和注意事项

### 2. [快速参考文档](OKX_API_QUICK_REFERENCE.md)
- 核心参数格式
- 常用操作示例
- 错误代码对照表
- 安全最佳实践

### 3. [参数验证工具](parameter_validator.py)
- 参数格式验证
- 自动参数格式化
- 支持合约列表
- 错误提示和修正

### 4. [安全订单指南](SAFE_ORDER_GUIDE.md)
- 确保只挂单不吃单的策略
- Post-Only订单使用方法
- 安全价格计算算法
- 最佳实践和注意事项

### 5. [智能下单系统指南](SMART_ORDER_SYSTEM_GUIDE.md)
- 智能订单监控和自动重试
- 超时撤单和重新提交机制
- 动态价格调整策略
- 完整的自动化交易解决方案

## 🚀 快速开始

### 环境配置
```bash
# 设置API配置
export OKX_API_KEY="your_api_key"
export OKX_SECRET_KEY="your_secret_key"
export OKX_PASSPHRASE="your_passphrase"
export OKX_SIMULATED="true"  # 启用模拟交易
```

### 基础使用
```python
from common_src.okx.okx_exchange import OkxFuturesAPI
from common_src.okx.parameter_validator import OkxParameterValidator

# 初始化API
api = OkxFuturesAPI(API_KEY, SECRET_KEY, PASSPHRASE, is_simulated=True)

# 使用参数验证器
validator = OkxParameterValidator()

# 创建限价单参数
params = validator.create_limit_order_params(
    symbol="BTC-USDT-SWAP",
    side="buy", 
    size="0.01",
    price="1000"
)

# 下单
result = api.place_order(**params)
```

## 📋 核心参数格式

### 限价单
```python
{
    "instId": "BTC-USDT-SWAP",    # 合约ID
    "tdMode": "cross",             # 交易模式
    "side": "buy",                 # 交易方向
    "ordType": "limit",            # 订单类型
    "px": "1000",                  # 价格
    "sz": "0.01"                  # 数量
}
```

### 市价单
```python
{
    "instId": "BTC-USDT-SWAP",
    "tdMode": "cross",
    "side": "buy", 
    "ordType": "market",           # 市价单
    "sz": "1"                     # 不需要价格
}
```

## 🧪 测试验证

### 运行完整测试
```bash
python common_src/okx/okx_test.py
```

### 运行API认证测试
```bash
python common_src/okx/test_api_auth.py
```

### 运行安全订单测试
```bash
python common_src/okx/test_safe_orders.py
```

### 运行智能下单系统测试
```bash
python common_src/okx/smart_order_system.py
python common_src/okx/test_smart_order_system.py
```

### 运行参数验证工具
```bash
python common_src/okx/parameter_validator.py
```

## 📊 测试结果

✅ **100% 成功率** - 所有8项测试全部通过

1. ✅ 服务器连通性测试
2. ✅ 账户权限测试  
3. ✅ 市场数据测试
4. ✅ 杠杆设置测试
5. ✅ 正确参数格式测试
6. ✅ 限价单买入测试
7. ✅ 限价单卖出测试
8. ✅ 持仓管理测试

## 🔧 工具和脚本

| 文件名 | 描述 |
|--------|------|
| `okx_exchange.py` | 核心API实现 |
| `okx_test.py` | 完整测试脚本 |
| `test_api_auth.py` | API认证测试 |
| `parameter_validator.py` | 参数验证工具 |
| `smart_order_system.py` | 智能下单系统 |
| `test_smart_order_system.py` | 智能下单系统测试 |
| `OKX_API_PARAMETERS.md` | 详细参数文档 |
| `OKX_API_QUICK_REFERENCE.md` | 快速参考文档 |
| `SAFE_ORDER_GUIDE.md` | 安全订单指南 |
| `SMART_ORDER_SYSTEM_GUIDE.md` | 智能下单系统指南 |

## ⚠️ 重要注意事项

1. **使用模拟交易模式** - 强烈推荐在测试时使用模拟交易
2. **参数验证** - 使用参数验证工具确保参数格式正确
3. **错误处理** - 实现完整的错误处理机制
4. **频率限制** - 注意API调用频率限制
5. **时间同步** - 确保本地时间与服务器时间同步

## 🛡️ 安全建议

- ✅ 使用模拟交易进行测试
- ✅ 验证所有输入参数
- ✅ 实现错误重试机制
- ✅ 记录详细的日志信息
- ✅ 定期检查API密钥权限

## 📞 支持

如有问题，请参考：
1. [详细参数文档](OKX_API_PARAMETERS.md) - 完整的使用说明
2. [快速参考文档](OKX_API_QUICK_REFERENCE.md) - 常用操作速查
3. [参数验证工具](parameter_validator.py) - 参数格式验证

---

**最后更新**: 2025-07-28  
**测试状态**: ✅ 100% 通过  
**推荐模式**: 模拟交易 