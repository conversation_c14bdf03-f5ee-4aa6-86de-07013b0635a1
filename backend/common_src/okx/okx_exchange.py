import json
import requests
import time
import hmac
import hashlib
import logging
from typing import Dict, Optional, Any, List
import sys
import os
import random
import string
from datetime import datetime
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from common_src.base_exchange import BaseExchange
from datetime import datetime, timezone
import base64

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OkxFuturesAPI(BaseExchange):
    def __init__(self, api_key: str, secret_key: str, passphrase: str, is_simulated: bool = False):
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
        self.base_url = "https://www.okx.com"
        self.server_time_offset = 0  # 服务器时间偏移量
        self.is_simulated = is_simulated  # 是否为模拟交易
        self.ping_server()
        logger.info("OkxFuturesAPI 初始化完成")

    def generate_client_order_id(self, prefix: str = "order", length: int = 16) -> str:
        """
        生成客户自定义订单ID
        
        Args:
            prefix: 订单ID前缀，默认为"order"
            length: 订单ID总长度，默认为16位（包含前缀）
        
        Returns:
            str: 符合OKX要求的客户自定义订单ID
            
        OKX要求：
        - 字母（区分大小写）与数字的组合
        - 可以是纯字母、纯数字
        - 长度要在1-32位之间
        """
        if length < 1 or length > 32:
            raise ValueError("订单ID长度必须在1-32位之间")
        
        if len(prefix) >= length:
            raise ValueError("前缀长度不能大于或等于总长度")
        
        # 计算剩余长度
        remaining_length = length - len(prefix)
        
        # 生成随机字符（字母和数字的组合）
        # 使用字母（大小写）和数字，避免特殊字符
        chars = string.ascii_letters + string.digits  # a-z, A-Z, 0-9
        
        # 生成随机字符串
        random_part = ''.join(random.choice(chars) for _ in range(remaining_length))
        
        # 组合前缀和随机部分
        client_order_id = prefix + random_part
        
        logger.debug(f"生成客户自定义订单ID: {client_order_id} (长度: {len(client_order_id)})")
        return client_order_id

    def generate_timestamped_order_id(self, prefix: str = "order") -> str:
        """
        生成带时间戳的客户自定义订单ID
        
        Args:
            prefix: 订单ID前缀，默认为"order"
        
        Returns:
            str: 包含时间戳的客户自定义订单ID
        """
        # 获取当前时间戳（毫秒）
        timestamp = int(time.time() * 1000)
        
        # 转换为16进制字符串（更短）
        timestamp_hex = hex(timestamp)[2:]  # 去掉'0x'前缀
        
        # 生成随机字符串（确保总长度不超过32位）
        max_random_length = 32 - len(prefix) - len(timestamp_hex)
        if max_random_length > 0:
            chars = string.ascii_letters + string.digits
            random_part = ''.join(random.choice(chars) for _ in range(max_random_length))
        else:
            random_part = ""
        
        # 组合：前缀 + 时间戳 + 随机字符串
        client_order_id = prefix + timestamp_hex + random_part
        
        # 确保长度不超过32位
        if len(client_order_id) > 32:
            client_order_id = client_order_id[:32]
        
        logger.debug(f"生成带时间戳的客户自定义订单ID: {client_order_id} (长度: {len(client_order_id)})")
        return client_order_id

    def generate_simple_order_id(self, prefix: str = "order") -> str:
        """
        生成简单的客户自定义订单ID
        
        Args:
            prefix: 订单ID前缀，默认为"order"
        
        Returns:
            str: 简单的客户自定义订单ID
        """
        # 生成纯字母数字的随机字符串
        chars = string.ascii_letters + string.digits  # 只使用字母和数字
        random_part = ''.join(random.choice(chars) for _ in range(8))
        
        # 组合：前缀 + 随机字符串
        client_order_id = f"{prefix}{random_part}"
        
        # 确保长度不超过32位
        if len(client_order_id) > 32:
            client_order_id = client_order_id[:32]
        
        logger.debug(f"生成简单客户自定义订单ID: {client_order_id} (长度: {len(client_order_id)})")
        return client_order_id

    def generate_unique_order_id(self, prefix: str = "order") -> str:
        """
        生成唯一的客户自定义订单ID（推荐用于生产环境）
        
        Args:
            prefix: 订单ID前缀，默认为"order"
        
        Returns:
            str: 唯一的客户自定义订单ID
        """
        # 获取当前时间戳（毫秒级精度）
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        
        # 生成较短的随机字符串（4位）
        chars = string.ascii_letters + string.digits
        random_part = ''.join(random.choice(chars) for _ in range(4))
        
        # 组合：前缀 + 时间戳 + 随机字符串
        client_order_id = f"{prefix}{timestamp}{random_part}"
        
        # 确保长度不超过32位
        if len(client_order_id) > 32:
            client_order_id = client_order_id[:32]
        
        logger.debug(f"生成唯一客户自定义订单ID: {client_order_id} (长度: {len(client_order_id)})")
        return client_order_id

    def generate_nano_order_id(self, prefix: str = "order") -> str:
        """
        生成纳秒级精度的客户自定义订单ID（最高唯一性）
        
        Args:
            prefix: 订单ID前缀，默认为"order"
        
        Returns:
            str: 纳秒级精度的客户自定义订单ID
        """
        import time
        # 获取纳秒级时间戳
        timestamp = int(time.time() * 1000000000)  # 纳秒级时间戳
        
        # 生成随机字符串（6位）
        chars = string.ascii_letters + string.digits
        random_part = ''.join(random.choice(chars) for _ in range(6))
        
        # 组合：前缀 + 时间戳 + 随机字符串
        client_order_id = f"{prefix}{timestamp}{random_part}"
        
        # 确保长度不超过32位
        if len(client_order_id) > 32:
            client_order_id = client_order_id[:32]
        
        logger.debug(f"生成纳秒级客户自定义订单ID: {client_order_id} (长度: {len(client_order_id)})")
        return client_order_id

    def generate_order_id_with_uuid(self, prefix: str = "order") -> str:
        """
        使用UUID生成客户自定义订单ID（最高唯一性保证）
        
        Args:
            prefix: 订单ID前缀，默认为"order"
        
        Returns:
            str: 使用UUID的客户自定义订单ID
        """
        import uuid
        
        # 生成UUID并转换为字符串
        uuid_str = str(uuid.uuid4()).replace('-', '')  # 移除连字符
        
        # 组合：前缀 + UUID
        client_order_id = f"{prefix}{uuid_str}"
        
        # 确保长度不超过32位
        if len(client_order_id) > 32:
            client_order_id = client_order_id[:32]
        
        logger.debug(f"生成UUID客户自定义订单ID: {client_order_id} (长度: {len(client_order_id)})")
        return client_order_id

    def _get_server_time(self):
        """获取OKX服务器时间"""
        try:
            response = requests.get(f'{self.base_url}/api/v5/public/time', timeout=5)
            response.raise_for_status()
            server_time = response.json().get('data', [{}])[0].get('ts', '0')
            # 添加小延迟确保时间戳不会过期
            time.sleep(0.1)
            return server_time
        except Exception as e:
            logger.warning(f"获取服务器时间失败: {e}，使用本地时间")
            return str(int(time.time() * 1000))

    def _parse_params_to_str(self, params: Dict[str, Any]) -> str:
        """将参数字典转换为查询字符串"""
        if not params:
            return ""
        url = '?'
        for key, value in params.items():
            if value != '' and value is not None:
                url = url + str(key) + '=' + str(value) + '&'
        url = url[0:-1]  # 移除最后的 '&'
        return url

    def generate_signature(self, timestamp: str, method: str, request_path: str, body: str) -> str:
        """生成签名"""
        # 使用大写方法名，与官方SDK保持一致
        method_upper = method.upper()
        message = f'{timestamp}{method_upper}{request_path}{body}'
        logger.info(f"签名消息: {message}")
        logger.info(f"Secret Key: {self.secret_key[:10]}...")
        
        # 使用HMAC-SHA256生成签名
        mac = hmac.new(
            bytes(self.secret_key, encoding='utf8'),
            bytes(message, encoding='utf-8'),
            digestmod='sha256'
        )
        signature = base64.b64encode(mac.digest()).decode('utf-8')
        logger.info(f"生成的签名: {signature}")
        return signature

    def _get_iso_timestamp(self):
        """获取ISO格式的时间戳"""
        # 获取服务器时间偏移量
        time_offset = self._get_server_time_offset()
        
        # 使用ISO格式的时间戳，格式为: 2023-12-19T08:09:27.000Z
        from datetime import datetime, timezone
        # 应用时间偏移
        adjusted_time = datetime.now(timezone.utc).timestamp() + (time_offset / 1000)
        current_time = datetime.fromtimestamp(adjusted_time, timezone.utc)
        timestamp = current_time.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        logger.debug(f"使用ISO时间戳: {timestamp} (偏移: {time_offset}ms)")
        return timestamp
    
    def _get_server_time_offset(self):
        """获取服务器时间偏移量"""
        try:
            response = requests.get(f'{self.base_url}/api/v5/public/time', timeout=10)
            response.raise_for_status()
            server_time = response.json().get('data', [{}])[0].get('ts', '0')
            local_time = int(time.time() * 1000)
            time_diff = int(server_time) - local_time
            logger.info(f"服务器时间: {server_time}, 本地时间: {local_time}, 时间差: {time_diff}ms")
            return time_diff
        except Exception as e:
            logger.warning(f"无法获取服务器时间偏移: {e}")
            return 0

    def _get_headers(self, method: str, request_path: str, body: str = "") -> Dict[str, str]:
        """生成请求头，包含签名认证"""
        # 使用ISO格式时间戳
        timestamp = self._get_iso_timestamp()
        sign = self.generate_signature(timestamp, method, request_path, body)
        headers = {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': sign,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json',
            'x-simulated-trading': '1' if self.is_simulated else '0'  # 根据实例设置模拟交易
        }
        logger.debug(f"请求头: {headers}")
        return headers

    def ping_server(self) -> None:
        try:
            response = requests.get(f'{self.base_url}/api/v5/public/time', timeout=10)
            response.raise_for_status()
            server_time = response.json().get('data', [{}])[0].get('ts', '0')
            local_time = str(int(time.time() * 1000))
            time_diff = abs(int(server_time) - int(local_time))
            logger.info(f"OKX服务器时间: {server_time}, 本地时间: {local_time}, 时间差: {time_diff}ms")
            if time_diff > 5000:  # 5秒
                logger.warning(f"本地时间与OKX服务器时间差异过大: {time_diff}ms")
            logger.info("OKX期货服务器连通性测试成功")
        except Exception as e:
            logger.error(f"无法连接到OKX期货服务器: {e}")
            raise ConnectionError(f"无法连接到OKX期货服务器: {e}")

    def set_leverage(self, symbol: str, leverage: int) -> Any:
        """
        设置杠杆倍率
        symbol: 合约ID，如 'BTC-USDT-SWAP'
        leverage: 杠杆倍数
        """
        url = f"{self.base_url}/api/v5/account/set-leverage"
        body = json.dumps({
            "instId": symbol,
            "lever": str(leverage),
            "mgnMode": "cross"  # 可根据需要调整为 'isolated'
        })
        headers = self._get_headers("POST", "/api/v5/account/set-leverage", body)
        logger.info(f"[set_leverage] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[set_leverage] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def set_margin_type(self, symbol: str, margin_type: str) -> Any:
        """
        设置仓位模式（仅支持全仓/逐仓切换，OKX需用 set-position-mode 接口，mode: 'net'/'long_short'）
        """
        url = f"{self.base_url}/api/v5/account/set-position-mode"
        mode = 'net' if margin_type.upper() == 'CROSSED' else 'long_short'
        body = json.dumps({"posMode": mode})
        headers = self._get_headers("POST", "/api/v5/account/set-position-mode", body)
        logger.info(f"[set_margin_type] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[set_margin_type] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def get_order_book(self, symbol: str, limit: int = 5) -> Dict[str, Any]:
        url = f"{self.base_url}/api/v5/market/books"
        params = {"instId": symbol, "sz": str(limit)}
        resp = requests.get(url, params=params, timeout=10)
        resp.raise_for_status()
        return resp.json()

    def place_order(self, instId: str, tdMode: str, side: str, ordType: str, sz: str, px: Optional[str] = None, clOrdId: Optional[str] = None, posSide: Optional[str] = None) -> Any:
        """
        下单接口
        instId: 合约ID，如 'BTC-USDT-SWAP'
        tdMode: 交易模式 'cross'/'isolated'
        side: 'buy'/'sell'
        ordType: 'limit'/'market' 等
        sz: 数量
        px: 价格（限价单必填）
        clOrdId: 客户自定义订单ID
        posSide: 持仓方向 'long'/'short'
        """
        url = f"{self.base_url}/api/v5/trade/order"
        data = {
            "instId": instId,
            "tdMode": tdMode,
            "side": side,
            "ordType": ordType,
            "sz": sz
        }
        if px:
            data["px"] = px
        if clOrdId:
            data["clOrdId"] = clOrdId
        if posSide:
            data["posSide"] = posSide
        body = json.dumps(data)
        
        # 在发送请求前重新生成时间戳，确保时间戳是最新的
        headers = self._get_headers("POST", "/api/v5/trade/order", body)
        logger.info(f"[place_order] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[place_order] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def cancel_order(self, symbol: str, order_id: int) -> Any:
        url = f"{self.base_url}/api/v5/trade/cancel-order"
        data = {"instId": symbol, "ordId": str(order_id)}
        body = json.dumps(data)
        headers = self._get_headers("POST", "/api/v5/trade/cancel-order", body)
        logger.info(f"[cancel_order] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[cancel_order] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def get_order_info(self, symbol: str, order_id: int) -> Dict[str, Any]:
        url = f"{self.base_url}/api/v5/trade/order"
        params = {"instId": symbol, "ordId": str(order_id)}
        
        # For GET requests, parameters are included in the request path
        request_path = "/api/v5/trade/order"
        if params:
            request_path += self._parse_params_to_str(params)
        
        # GET请求的body为空字符串
        headers = self._get_headers("GET", request_path, "")
        logger.info(f"[get_order_info] url={url}, request_path={request_path}, headers={headers}, params={params}")
        resp = requests.get(url, headers=headers, params=params, timeout=10)
        logger.info(f"[get_order_info] status={resp.status_code}, response={resp.text}")
        return resp.json()

    def query_position_info(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        url = f"{self.base_url}/api/v5/account/positions"
        params = {"instType": "SWAP"}
        if symbol:
            params["instId"] = symbol
        
        # 使用统一的参数构建方法
        request_path = "/api/v5/account/positions"
        request_path += self._parse_params_to_str(params)
        
        # GET请求的body为空字符串
        headers = self._get_headers("GET", request_path, "")
        logger.info(f"[query_position_info] url={url}, headers={headers}, params={params}")
        resp = requests.get(url, headers=headers, params=params, timeout=10)
        logger.info(f"[query_position_info] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        data = resp.json()
        return data.get("data", [])

    def query_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        url = f"{self.base_url}/api/v5/trade/orders-pending"
        params = {}
        if symbol:
            params["instId"] = symbol
        
        # 使用统一的参数构建方法
        request_path = "/api/v5/trade/orders-pending"
        request_path += self._parse_params_to_str(params)
        
        # GET请求的body为空字符串
        headers = self._get_headers("GET", request_path, "")
        logger.info(f"[query_open_orders] url={url}, headers={headers}, params={params}")
        resp = requests.get(url, headers=headers, params=params, timeout=10)
        logger.info(f"[query_open_orders] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        data = resp.json()
        return data.get("data", [])

    def clear_order(self) -> None:
        """
        清理所有持仓和未成交订单
        """
        # 取消所有未成交订单
        open_orders = self.query_open_orders()
        for order in open_orders:
            self.cancel_order(order["instId"], order["ordId"])
        # 平掉所有持仓（此处仅示例，实际应根据持仓方向下反向单）
        positions = self.query_position_info()
        for pos in positions:
            instId = pos["instId"]
            pos_side = pos.get("posSide", "net")
            sz = pos.get("pos", "0")
            if float(sz) != 0:
                side = "sell" if float(sz) > 0 else "buy"
                self.place_order(instId, pos.get("mgnMode", "cross"), side, "market", str(abs(float(sz))))
   
    def open_position_with_custom_order_id(self, symbol: str, side: str, size: str, price: str, 
                                         order_type: str = 'limit', prefix: str = "open", 
                                         unique_method: str = "unique") -> Dict[str, Any]:
        """
        使用自定义订单号进行开仓
        
        Args:
            symbol: 交易对，如 'IP-USDT-SWAP'
            side: 交易方向，'buy' 或 'sell'
            size: 交易数量
            price: 价格
            order_type: 订单类型，默认为 'limit'
            prefix: 自定义订单ID前缀，默认为 "open"
            unique_method: 唯一性生成方法，可选值: "simple", "unique", "nano", "uuid"
        
        Returns:
            Dict: 开仓结果
        """
        try:
            # 根据方法生成自定义订单ID
            if unique_method == "simple":
                client_order_id = self.generate_simple_order_id(prefix=prefix)
            elif unique_method == "unique":
                client_order_id = self.generate_unique_order_id(prefix=prefix)
            elif unique_method == "nano":
                client_order_id = self.generate_nano_order_id(prefix=prefix)
            elif unique_method == "uuid":
                client_order_id = self.generate_order_id_with_uuid(prefix=prefix)
            else:
                client_order_id = self.generate_unique_order_id(prefix=prefix)
            
            logger.info(f"生成开仓订单ID: {client_order_id} (方法: {unique_method})")
            
            # 构建订单参数
            order_params = {
                "instId": symbol,
                "tdMode": "cross",
                "side": side,
                "ordType": order_type,
                "sz": size,
                "clOrdId": client_order_id
            }
            
            # 如果是限价单，添加价格
            if order_type == 'limit':
                order_params["px"] = price
            
            # 下单
            result = self.place_order(**order_params)
            
            logger.info(f"开仓结果: {result}")
            return {
                "success": result.get('code') == '0',
                "client_order_id": client_order_id,
                "order_id": result.get('data', [{}])[0].get('ordId', ''),
                "result": result
            }
            
        except Exception as e:
            logger.error(f"开仓失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "client_order_id": client_order_id if 'client_order_id' in locals() else None
            }

    def close_position_with_custom_order_id(self, symbol: str, side: str, size: str, price: str,
                                          order_type: str = 'limit', prefix: str = "close",
                                          unique_method: str = "unique") -> Dict[str, Any]:
        """
        使用自定义订单号进行平仓
        
        Args:
            symbol: 交易对，如 'IP-USDT-SWAP'
            side: 交易方向，'buy' 或 'sell'（与开仓方向相反）
            size: 交易数量
            price: 价格
            order_type: 订单类型，默认为 'limit'
            prefix: 自定义订单ID前缀，默认为 "close"
            unique_method: 唯一性生成方法，可选值: "simple", "unique", "nano", "uuid"
        
        Returns:
            Dict: 平仓结果
        """
        try:
            # 根据方法生成自定义订单ID
            if unique_method == "simple":
                client_order_id = self.generate_simple_order_id(prefix=prefix)
            elif unique_method == "unique":
                client_order_id = self.generate_unique_order_id(prefix=prefix)
            elif unique_method == "nano":
                client_order_id = self.generate_nano_order_id(prefix=prefix)
            elif unique_method == "uuid":
                client_order_id = self.generate_order_id_with_uuid(prefix=prefix)
            else:
                client_order_id = self.generate_unique_order_id(prefix=prefix)
            
            logger.info(f"生成平仓订单ID: {client_order_id} (方法: {unique_method})")
            
            # 构建订单参数
            order_params = {
                "instId": symbol,
                "tdMode": "cross",
                "side": side,
                "ordType": order_type,
                "sz": size,
                "clOrdId": client_order_id
            }
            
            # 如果是限价单，添加价格
            if order_type == 'limit':
                order_params["px"] = price
            
            # 下单
            result = self.place_order(**order_params)
            
            logger.info(f"平仓结果: {result}")
            return {
                "success": result.get('code') == '0',
                "client_order_id": client_order_id,
                "order_id": result.get('data', [{}])[0].get('ordId', ''),
                "result": result
            }
            
        except Exception as e:
            logger.error(f"平仓失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "client_order_id": client_order_id if 'client_order_id' in locals() else None
            }

    def open_and_close_position(self, symbol: str, size: str, open_price: str, close_price: str,
                              order_type: str = 'limit') -> Dict[str, Any]:
        """
        开仓并立即平仓（对冲交易）
        
        Args:
            symbol: 交易对，如 'IP-USDT-SWAP'
            size: 交易数量
            open_price: 开仓价格
            close_price: 平仓价格
            order_type: 订单类型，默认为 'limit'
        
        Returns:
            Dict: 开仓和平仓结果
        """
        try:
            # 获取订单簿来计算最优挂单价
            order_book = self.get_order_book(symbol, limit=5)
            if not order_book.get('data') or not order_book['data']:
                return {"success": False, "error": "无法获取订单簿数据"}
            
            # 计算最优挂单价
            best_bid = float(order_book['data'][0]['bids'][0][0]) if order_book['data'][0]['bids'] else None
            best_ask = float(order_book['data'][0]['asks'][0][0]) if order_book['data'][0]['asks'] else None
            
            if not best_bid or not best_ask:
                return {"success": False, "error": "无法获取有效的买卖价格"}
            
            # 计算最优开仓价格（略低于买一价）
            optimal_open_price = str(best_bid * 0.999)  # 比买一价低0.1%
            
            # 计算最优平仓价格（略高于卖一价）
            optimal_close_price = str(best_ask * 1.001)  # 比卖一价高0.1%
            
            logger.info(f"订单簿数据 - 买一价: {best_bid}, 卖一价: {best_ask}")
            logger.info(f"计算的最优价格 - 开仓价: {optimal_open_price}, 平仓价: {optimal_close_price}")
            
            # 开仓（买入）
            open_result = self.open_position_with_custom_order_id(
                symbol=symbol,
                side='buy',
                size=size,
                price=optimal_open_price,
                order_type=order_type,
                prefix="open"
            )
            
            if not open_result['success']:
                return {
                    "success": False,
                    "error": f"开仓失败: {open_result.get('error', open_result.get('result', {}).get('msg', '未知错误'))}",
                    "open_result": open_result
                }
            
            # 平仓（卖出）
            close_result = self.close_position_with_custom_order_id(
                symbol=symbol,
                side='sell',
                size=size,
                price=optimal_close_price,
                order_type=order_type,
                prefix="close"
            )
            
            return {
                "success": open_result['success'] and close_result['success'],
                "open_result": open_result,
                "close_result": close_result,
                "summary": {
                    "open_order_id": open_result.get('client_order_id'),
                    "close_order_id": close_result.get('client_order_id'),
                    "open_price": optimal_open_price,
                    "close_price": optimal_close_price,
                    "size": size,
                    "market_data": {
                        "best_bid": best_bid,
                        "best_ask": best_ask,
                        "spread": best_ask - best_bid
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"开仓平仓失败: {e}")
            return {"success": False, "error": str(e)}

    def get_order_by_client_order_id(self, symbol: str, client_order_id: str) -> Dict[str, Any]:
        """
        通过客户自定义订单ID查询订单
        
        Args:
            symbol: 交易对
            client_order_id: 客户自定义订单ID
        
        Returns:
            Dict: 订单信息
        """
        try:
            url = f"{self.base_url}/api/v5/trade/order"
            params = {"instId": symbol, "clOrdId": client_order_id}
            
            # 构建请求路径
            request_path = "/api/v5/trade/order"
            if params:
                request_path += self._parse_params_to_str(params)
            
            headers = self._get_headers("GET", request_path, "")
            logger.info(f"[get_order_by_client_order_id] url={url}, request_path={request_path}, params={params}")
            
            resp = requests.get(url, headers=headers, params=params, timeout=10)
            logger.info(f"[get_order_by_client_order_id] status={resp.status_code}, response={resp.text}")
            
            return resp.json()
            
        except Exception as e:
            logger.error(f"查询订单失败: {e}")
            return {"error": str(e)}

    def cancel_order_by_client_order_id(self, symbol: str, client_order_id: str) -> Dict[str, Any]:
        """
        通过客户自定义订单ID撤销订单
        
        Args:
            symbol: 交易对
            client_order_id: 客户自定义订单ID
        
        Returns:
            Dict: 撤销结果
        """
        try:
            url = f"{self.base_url}/api/v5/trade/cancel-order"
            body = {"instId": symbol, "clOrdId": client_order_id}
            
            headers = self._get_headers("POST", "/api/v5/trade/cancel-order", json.dumps(body))
            logger.info(f"[cancel_order_by_client_order_id] url={url}, body={body}")
            
            resp = requests.post(url, headers=headers, json=body, timeout=10)
            logger.info(f"[cancel_order_by_client_order_id] status={resp.status_code}, response={resp.text}")
            
            return resp.json()
            
        except Exception as e:
            logger.error(f"撤销订单失败: {e}")
            return {"error": str(e)}
    
    def get_account_config(self) -> Dict[str, Any]:
        """
        获取账户配置信息
        
        Returns:
            Dict: 账户配置信息
        """
        try:
            url = f"{self.base_url}/api/v5/account/config"
            
            method = 'GET'
            request_path = '/api/v5/account/config'
            
            headers = self._get_headers(method, request_path)
            
            logger.info(f"[get_account_config] url={url}, headers={headers}")
            
            response = requests.get(url, headers=headers)
            logger.info(f"[get_account_config] status={response.status_code}, response={response.text}")
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取账户配置失败: {response.status_code} - {response.text}")
                return {"error": f"HTTP {response.status_code}", "message": response.text}
                
        except Exception as e:
            logger.error(f"获取账户配置异常: {e}")
            return {"error": str(e)}
    
    def get_account_balance(self) -> Dict[str, Any]:
        """
        获取账户余额信息
        
        Returns:
            Dict: 账户余额信息
        """
        try:
            url = f"{self.base_url}/api/v5/account/balance"
            
            method = 'GET'
            request_path = '/api/v5/account/balance'
            
            headers = self._get_headers(method, request_path)
            
            logger.info(f"[get_account_balance] url={url}, headers={headers}")
            
            response = requests.get(url, headers=headers)
            logger.info(f"[get_account_balance] status={response.status_code}, response={response.text}")
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取账户余额失败: {response.status_code} - {response.text}")
                return {"error": f"HTTP {response.status_code}", "message": response.text}
                
        except Exception as e:
            logger.error(f"获取账户余额异常: {e}")
            return {"error": str(e)}

    def place_spot_order(self, instId: str, side: str, ordType: str, sz: str, px: Optional[str] = None, clOrdId: Optional[str] = None) -> Any:
        """
        现货交易下单接口
        
        Args:
            instId: 现货交易对，如 'BTC-USDT'
            side: 'buy'/'sell'
            ordType: 'limit'/'market' 等
            sz: 数量
            px: 价格（限价单必填）
            clOrdId: 客户自定义订单ID
        
        Returns:
            Dict: 下单结果
        """
        url = f"{self.base_url}/api/v5/trade/order"
        data = {
            "instId": instId,
            "tdMode": "cash",  # 现货交易模式
            "side": side,
            "ordType": ordType,
            "sz": sz
        }
        if px:
            data["px"] = px
        if clOrdId:
            data["clOrdId"] = clOrdId
        body = json.dumps(data)
        
        # 在发送请求前重新生成时间戳，确保时间戳是最新的
        headers = self._get_headers("POST", "/api/v5/trade/order", body)
        logger.info(f"[place_spot_order] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[place_spot_order] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def get_spot_order_info(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """
        获取现货订单信息
        
        Args:
            symbol: 现货交易对，如 'BTC-USDT'
            order_id: 订单ID
        
        Returns:
            Dict: 订单信息
        """
        url = f"{self.base_url}/api/v5/trade/order"
        params = {"instId": symbol, "ordId": str(order_id)}
        
        # For GET requests, parameters are included in the request path
        request_path = "/api/v5/trade/order"
        if params:
            request_path += self._parse_params_to_str(params)
        
        # GET请求的body为空字符串
        headers = self._get_headers("GET", request_path, "")
        logger.info(f"[get_spot_order_info] url={url}, request_path={request_path}, headers={headers}, params={params}")
        resp = requests.get(url, headers=headers, params=params, timeout=10)
        logger.info(f"[get_spot_order_info] status={resp.status_code}, response={resp.text}")
        return resp.json()

    def cancel_spot_order(self, symbol: str, order_id: int) -> Any:
        """
        撤销现货订单
        
        Args:
            symbol: 现货交易对，如 'BTC-USDT'
            order_id: 订单ID
        
        Returns:
            Dict: 撤销结果
        """
        url = f"{self.base_url}/api/v5/trade/cancel-order"
        data = {"instId": symbol, "ordId": str(order_id)}
        body = json.dumps(data)
        headers = self._get_headers("POST", "/api/v5/trade/cancel-order", body)
        logger.info(f"[cancel_spot_order] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[cancel_spot_order] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def get_spot_account_balance(self) -> Dict[str, Any]:
        """
        获取现货账户余额
        
        Returns:
            Dict: 账户余额信息
        """
        url = f"{self.base_url}/api/v5/account/balance"
        request_path = "/api/v5/account/balance"
        headers = self._get_headers("GET", request_path, "")
        logger.info(f"[get_spot_account_balance] url={url}, headers={headers}")
        resp = requests.get(url, headers=headers, timeout=10)
        logger.info(f"[get_spot_account_balance] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def place_post_only_order(self, instId: str, tdMode: str, side: str, sz: str, px: str, 
                             clOrdId: Optional[str] = None, posSide: Optional[str] = None) -> Any:
        """
        下Post-Only订单（只挂单不吃单）
        
        Args:
            instId: 合约ID，如 'BTC-USDT-SWAP'
            tdMode: 交易模式 'cross'/'isolated'
            side: 'buy'/'sell'
            sz: 数量
            px: 价格
            clOrdId: 客户自定义订单ID
            posSide: 持仓方向 'long'/'short'
        
        Returns:
            Dict: 下单结果
        """
        url = f"{self.base_url}/api/v5/trade/order"
        data = {
            "instId": instId,
            "tdMode": tdMode,
            "side": side,
            "ordType": "post_only",  # 使用post_only订单类型
            "sz": sz,
            "px": px
        }
        if clOrdId:
            data["clOrdId"] = clOrdId
        if posSide:
            data["posSide"] = posSide
        body = json.dumps(data)
        
        # 在发送请求前重新生成时间戳，确保时间戳是最新的
        headers = self._get_headers("POST", "/api/v5/trade/order", body)
        logger.info(f"[place_post_only_order] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[place_post_only_order] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def place_fok_order(self, instId: str, tdMode: str, side: str, sz: str, px: str,
                       clOrdId: Optional[str] = None, posSide: Optional[str] = None) -> Any:
        """
        下FOK订单（Fill or Kill，要么全部成交要么全部取消）
        
        Args:
            instId: 合约ID，如 'BTC-USDT-SWAP'
            tdMode: 交易模式 'cross'/'isolated'
            side: 'buy'/'sell'
            sz: 数量
            px: 价格
            clOrdId: 客户自定义订单ID
            posSide: 持仓方向 'long'/'short'
        
        Returns:
            Dict: 下单结果
        """
        url = f"{self.base_url}/api/v5/trade/order"
        data = {
            "instId": instId,
            "tdMode": tdMode,
            "side": side,
            "ordType": "fok",  # 使用fok订单类型
            "sz": sz,
            "px": px
        }
        if clOrdId:
            data["clOrdId"] = clOrdId
        if posSide:
            data["posSide"] = posSide
        body = json.dumps(data)
        
        # 在发送请求前重新生成时间戳，确保时间戳是最新的
        headers = self._get_headers("POST", "/api/v5/trade/order", body)
        logger.info(f"[place_fok_order] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[place_fok_order] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def place_ioc_order(self, instId: str, tdMode: str, side: str, sz: str, px: str,
                       clOrdId: Optional[str] = None, posSide: Optional[str] = None) -> Any:
        """
        下IOC订单（Immediate or Cancel，立即成交或取消）
        
        Args:
            instId: 合约ID，如 'BTC-USDT-SWAP'
            tdMode: 交易模式 'cross'/'isolated'
            side: 'buy'/'sell'
            sz: 数量
            px: 价格
            clOrdId: 客户自定义订单ID
            posSide: 持仓方向 'long'/'short'
        
        Returns:
            Dict: 下单结果
        """
        url = f"{self.base_url}/api/v5/trade/order"
        data = {
            "instId": instId,
            "tdMode": tdMode,
            "side": side,
            "ordType": "ioc",  # 使用ioc订单类型
            "sz": sz,
            "px": px
        }
        if clOrdId:
            data["clOrdId"] = clOrdId
        if posSide:
            data["posSide"] = posSide
        body = json.dumps(data)
        
        # 在发送请求前重新生成时间戳，确保时间戳是最新的
        headers = self._get_headers("POST", "/api/v5/trade/order", body)
        logger.info(f"[place_ioc_order] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[place_ioc_order] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def place_limit_order_only(self, instId: str, tdMode: str, side: str, sz: str, px: str,
                             clOrdId: Optional[str] = None, posSide: Optional[str] = None) -> Any:
        """
        下限价单（只挂单不吃单）- 通过价格策略确保
        
        Args:
            instId: 合约ID，如 'BTC-USDT-SWAP'
            tdMode: 交易模式 'cross'/'isolated'
            side: 'buy'/'sell'
            sz: 数量
            px: 价格
            clOrdId: 客户自定义订单ID
            posSide: 持仓方向 'long'/'short'
        
        Returns:
            Dict: 下单结果
        """
        url = f"{self.base_url}/api/v5/trade/order"
        data = {
            "instId": instId,
            "tdMode": tdMode,
            "side": side,
            "ordType": "limit",  # 使用limit订单类型
            "sz": sz,
            "px": px
        }
        if clOrdId:
            data["clOrdId"] = clOrdId
        if posSide:
            data["posSide"] = posSide
        body = json.dumps(data)
        
        # 在发送请求前重新生成时间戳，确保时间戳是最新的
        headers = self._get_headers("POST", "/api/v5/trade/order", body)
        logger.info(f"[place_limit_order_only] url={url}, headers={headers}, body={body}")
        resp = requests.post(url, headers=headers, data=body, timeout=10)
        logger.info(f"[place_limit_order_only] status={resp.status_code}, response={resp.text}")
        resp.raise_for_status()
        return resp.json()

    def calculate_safe_limit_price(self, symbol: str, side: str, price_offset_percent: float = 0.1) -> Dict[str, Any]:
        """
        计算安全的限价单价格（确保只挂单不吃单）
        
        Args:
            symbol: 合约ID
            side: 交易方向 'buy'/'sell'
            price_offset_percent: 价格偏移百分比
        
        Returns:
            Dict: 包含安全价格和订单簿信息的字典
        """
        try:
            # 获取订单簿数据
            order_book = self.get_order_book(symbol, limit=5)
            if not order_book.get('data') or not order_book['data']:
                return {"success": False, "error": "无法获取订单簿数据"}
            
            best_bid = float(order_book['data'][0]['bids'][0][0]) if order_book['data'][0]['bids'] else None
            best_ask = float(order_book['data'][0]['asks'][0][0]) if order_book['data'][0]['asks'] else None
            
            if not best_bid or not best_ask:
                return {"success": False, "error": "无法获取有效的买卖价格"}
            
            # 计算安全价格
            if side == 'buy':
                # 买单：价格低于买一价，确保只挂单不吃单
                safe_price = best_bid * (1 - price_offset_percent / 100)
                price_type = "buy_limit_below_bid"
            else:
                # 卖单：价格高于卖一价，确保只挂单不吃单
                safe_price = best_ask * (1 + price_offset_percent / 100)
                price_type = "sell_limit_above_ask"
            
            logger.info(f"订单簿数据 - 买一价: {best_bid}, 卖一价: {best_ask}")
            logger.info(f"计算的安全价格 - {side}单: {safe_price} ({price_type})")
            
            return {
                "success": True,
                "safe_price": str(safe_price),
                "price_type": price_type,
                "market_data": {
                    "best_bid": best_bid,
                    "best_ask": best_ask,
                    "spread": best_ask - best_bid,
                    "spread_percent": ((best_ask - best_bid) / best_bid) * 100
                }
            }
            
        except Exception as e:
            logger.error(f"计算安全价格失败: {e}")
            return {"success": False, "error": str(e)}

    def place_safe_limit_order(self, symbol: str, side: str, size: str, 
                             price_offset_percent: float = 0.1,
                             clOrdId: Optional[str] = None) -> Dict[str, Any]:
        """
        下安全的限价单（确保只挂单不吃单）
        
        Args:
            symbol: 合约ID
            side: 交易方向 'buy'/'sell'
            size: 数量
            price_offset_percent: 价格偏移百分比
            clOrdId: 自定义订单ID
        
        Returns:
            Dict: 下单结果
        """
        try:
            # 计算安全价格
            price_result = self.calculate_safe_limit_price(symbol, side, price_offset_percent)
            if not price_result['success']:
                return price_result
            
            safe_price = price_result['safe_price']
            
            # 下安全的限价单
            order_result = self.place_limit_order_only(
                instId=symbol,
                tdMode="cross",
                side=side,
                sz=size,
                px=safe_price,
                clOrdId=clOrdId
            )
            
            logger.info(f"安全限价单结果: {order_result}")
            
            if order_result.get('code') == '0':
                return {
                    "success": True,
                    "order_id": order_result.get('data', [{}])[0].get('ordId', ''),
                    "safe_price": safe_price,
                    "price_type": price_result['price_type'],
                    "market_data": price_result['market_data'],
                    "order_result": order_result
                }
            else:
                return {
                    "success": False,
                    "error": order_result.get('msg', '下单失败'),
                    "order_result": order_result
                }
                
        except Exception as e:
            logger.error(f"下安全限价单失败: {e}")
            return {"success": False, "error": str(e)}

    def place_post_only_order_safe(self, symbol: str, side: str, size: str, price: str,
                                 clOrdId: Optional[str] = None) -> Dict[str, Any]:
        """
        下Post-Only订单（确保只挂单不吃单）
        
        Args:
            symbol: 合约ID
            side: 交易方向 'buy'/'sell'
            size: 数量
            price: 价格
            clOrdId: 自定义订单ID
        
        Returns:
            Dict: 下单结果
        """
        try:
            # 下Post-Only订单
            order_result = self.place_post_only_order(
                instId=symbol,
                tdMode="cross",
                side=side,
                sz=size,
                px=price,
                clOrdId=clOrdId
            )
            
            logger.info(f"Post-Only订单结果: {order_result}")
            
            if order_result.get('code') == '0':
                return {
                    "success": True,
                    "order_id": order_result.get('data', [{}])[0].get('ordId', ''),
                    "order_type": "post_only",
                    "order_result": order_result
                }
            else:
                return {
                    "success": False,
                    "error": order_result.get('msg', 'Post-Only下单失败'),
                    "order_result": order_result
                }
                
        except Exception as e:
            logger.error(f"Post-Only下单失败: {e}")
            return {"success": False, "error": str(e)}
   