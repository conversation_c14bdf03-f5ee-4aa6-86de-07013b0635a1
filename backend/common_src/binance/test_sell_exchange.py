

import json
import logging
import time
from exchange import BinanceFuturesAPI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


if __name__ == "__main__":
    api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
    secret_key = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

    # 初始化API客户端
    print("\n1. 初始化API客户端...")
    futures_api = BinanceFuturesAPI(api_key, secret_key)
    print("✅ API客户端初始化成功")

    futures_api.place_sell_order(symbol='IPUSDC', order_id=410894370, leverage=2)