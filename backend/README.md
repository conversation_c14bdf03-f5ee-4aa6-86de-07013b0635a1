# 加密货币合约信息获取工具

这个工具用于获取各大加密货币交易所的合约交易对信息，包括资金费率等关键数据，并存储到Redis数据库中供其他服务使用。

## 功能特点

- 支持多家交易所数据获取
- 自动获取合约交易对信息和资金费率
- 数据存储到Redis，方便其他应用程序使用
- 支持代理配置，解决网络访问问题
- 自动重试机制，增强数据获取稳定性
- 详细日志记录，便于问题排查

## 环境
Python版本 3.10

| 创建虚拟环境 | `virtualenv -p python3.10 env`                     |
| ------ | -------------------------------------------------- |
| 激活环境   | `source env/bin/activate` 或 `env\Scripts\activate` |
| 导出依赖   | `pip freeze > requirements.txt`                    |
| 安装依赖   | `pip install -r requirements.txt`                  |

```bash
pip install -r requirements.txt
```

## 配置文件

配置文件位于 `config.py`，包含以下配置项：

### Redis 配置

```python
REDIS_CONFIG = {
    'host': 'localhost',         # Redis 服务器地址
    'port': 6379,                # Redis 端口
    'db': 0,                     # Redis 数据库
    'password': None,            # Redis 密码（如果有）
    'decode_responses': True     # 自动解码响应
}
```

### 代理配置

```python
PROXY_CONFIG = {
    'enabled': True,             # 是否启用代理
    'host': 'proxy.example.com', # 代理服务器地址
    'port': '8080',              # 代理端口
    'username': 'user',          # 代理用户名（如果需要认证）
    'password': 'pass'           # 代理密码（如果需要认证）
}
```

### 日志配置

```python
LOGGING_CONFIG = {
    'log_dir': 'logs',           # 日志目录
    'level': 'INFO',             # 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
}
```

### 合约更新配置

```python
CONTRACT_CONFIG = {
    'update_interval': 180,      # 数据更新间隔（秒）
}
```

### Redis键名配置

```python
REDIS_KEYS = {
    'contracts_dir': 'contracts',                   # Redis键名前缀
    'exchange_results': 'contracts:exchange_results', # 交易所执行结果的键名
}
```

## 使用方法

### 启动参数说明

程序支持以下命令行参数：

| 参数 | 说明 | 示例 |
|------|------|------|
| `--debug` | 启用调试模式 | `python src/main.py --debug` |
| `--exchange` | 指定要调试的交易所（binance/okx/bitget） | `python src/main.py --debug --exchange bitget` |
| `--interval` | 设置数据更新间隔（秒） | `python src/main.py --debug --exchange okx --interval 300` |

使用示例：

1. 调试 Bitget 交易所：
```bash
python src/main.py --debug --exchange bitget
```

2. 调试 OKX 交易所，并设置 5 分钟更新间隔：
```bash
python src/main.py --debug --exchange okx --interval 300
```

3. 调试 Binance 交易所，并设置 1 分钟更新间隔：
```bash
python src/main.py --debug --exchange binance --interval 60
```

### 获取所有支持的交易所的合约信息

```bash
python fetch_contract_symbols.py
```

### 获取特定交易所的合约信息

```bash
python fetch_contract_symbols.py --exchanges binance okx bybit
```

## Redis数据结构

数据以以下结构存储在Redis中：

### 1. 合约数据哈希表

键名: `contracts:contracts_data`

该哈希表包含所有合约的详细信息，键格式为 `{exchange_id}:{symbol}`，值为JSON格式的合约详细信息。

示例数据结构：
```json
{
  "id": "BTC-USDT-SWAP",        // 合约唯一标识符
  "exchange": "okx",            // 交易所名称
  "symbol": "BTC/USDT:USDT",    // 交易对符号
  "base": "BTC",                // 基础货币
  "quote": "USDT",              // 计价货币
  "contract_name": true,        // 是否为合约交易
  "type": "swap",               // 合约类型（永续合约）
  "active": true,               // 合约是否处于活跃状态
  "funding_rate": 0.0001,       // 当前资金费率
  "funding_timestamp": 1632816000000,  // 资金费率时间戳（毫秒）
  "funding_datetime": "2021-09-28T08:00:00.000Z",  // 资金费率时间（ISO格式）
  "funding_rate_upper_limit": null,    // 资金费率上限
  "funding_rate_lower_limit": null,    // 资金费率下限
  "funding_period": null,              // 资金费率结算周期
  "precision": {                       // 精度设置
    "price": 0.1,                      // 价格精度
    "amount": 1                        // 数量精度
  },
  "limits": {                          // 交易限制
    "amount": {                        // 数量限制
      "min": 1,                        // 最小交易数量
      "max": null                      // 最大交易数量
    },
    "price": {                         // 价格限制
      "min": 0.1,                      // 最小价格
      "max": null                      // 最大价格
    }
  }
}
```

### 2. 合约符号队列

键名: `contracts:symbols_queue`

该列表包含所有合约的唯一标识符，格式为 `{exchange_id}:{symbol}`。

### 3. 交易所合约哈希表

键名: `contracts:{exchange_id}:contracts`

该哈希表包含特定交易所的所有合约详细信息，键为合约符号，值为JSON格式的合约详细信息。

### 4. 交易所合约符号列表

键名: `contracts:{exchange_id}:symbols`

该列表包含特定交易所的所有合约符号。

### 5. 交易所执行结果

键名: `contracts:exchange_results`

该哈希表包含每个交易所的执行结果统计信息，键为交易所ID，值为JSON格式的执行结果。

示例数据结构：
```json
{
  "exchange_id": "okx",
  "total_contracts": 300,
  "timestamp": "2023-09-28T08:00:00.000Z",
  "status": "success",
  "contracts": ["BTC/USDT:USDT", "ETH/USDT:USDT", ...],
  "error": null
}
```

## API接口

该工具提供了以下API接口，可以在其他程序中使用：

1. `get_contract_info(symbol, exchange_id=None)` - 获取特定合约的详细信息
2. `get_all_symbols(exchange_id=None)` - 获取所有合约符号列表
3. `get_exchange_contracts(exchange_id)` - 获取指定交易所的所有合约信息
4. `get_exchange_contract_symbols(exchange_id)` - 获取指定交易所的所有合约符号列表
5. `get_exchange_contract_info(exchange_id, symbol)` - 获取指定交易所特定合约的详细信息
6. `get_exchange_result(exchange_id)` - 获取特定交易所的执行结果
7. `get_all_exchange_results()` - 获取所有交易所的执行结果

## 日志文件

日志文件存储在 `logs` 目录下，每次运行程序会创建一个新的日志文件，文件名格式为 `contract_fetcher_YYYYMMDD_HHMMSS.log`。

## 故障排除

1. 如果遇到网络问题，请检查代理配置是否正确
2. 如果无法连接到Redis，请检查Redis配置是否正确
3. 如果获取特定交易所的数据失败，可能是该交易所API有变化，请更新CCXT库

## 开发者

如需贡献代码或报告问题，请联系项目维护者。 